Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(.text) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    main.o(.text) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(.text) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    main.o(.text) refers to _printf_str.o(.text) for _printf_str
    main.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(.text) refers to noretval__2sprintf.o(.text) for __2sprintf
    main.o(.text) refers to lcd.o(.text) for lcd_fill
    main.o(.text) refers to strlen.o(.text) for strlen
    main.o(.text) refers to ad9833.o(.text) for AD9833_SetFrequencyQuick1
    main.o(.text) refers to dac.o(.text) for DAC_GetUserEnable
    main.o(.text) refers to stm32f4xx_adc.o(.text) for ADC_Cmd
    main.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_Cmd
    main.o(.text) refers to noretval__2printf.o(.text) for __2printf
    main.o(.text) refers to lcd.o(.data) for g_point_color
    main.o(.text) refers to main.o(.data) for buttons
    main.o(.text) refers to main.o(.bss) for adc3_sample_buffer
    main.o(.text) refers to main.o(.conststring) for .conststring
    main.o(.text) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    main.o(.text) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    main.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    main.o(.text) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    main.o(.text) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    main.o(.text) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    main.o(.text) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    main.o(.text) refers to arm_cfft_f32.o(.text) for arm_cfft_f32
    main.o(.text) refers to main.o(.constdata) for arm_cfft_sR_f32_len512
    main.o(.text) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    main.o(.text) refers to log10f.o(i.__hardfp_log10f) for __hardfp_log10f
    main.o(.text) refers to arm_cmplx_mag_f32.o(.text) for arm_cmplx_mag_f32
    main.o(.text) refers to lcd.o(.bss) for lcddev
    main.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    main.o(.text) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    main.o(.text) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    main.o(.text) refers to timer.o(.text) for TIM5_ADC3_Init
    main.o(.text) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    main.o(.text) refers to log.o(i.__hardfp_log) for __hardfp_log
    main.o(.text) refers to sinh.o(i.__hardfp_sinh) for __hardfp_sinh
    main.o(.text) refers to delay.o(.text) for delay_ms
    main.o(.text) refers to arm_cfft_radix4_init_f32.o(.text) for arm_cfft_radix4_init_f32
    main.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    main.o(.text) refers to usart.o(.text) for uart_init
    main.o(.text) refers to led.o(.text) for LED_Init
    main.o(.text) refers to adc.o(.text) for Adc_Init
    main.o(.text) refers to stm32f4_key.o(.text) for key_config
    main.o(.text) refers to fft.o(.bss) for scfft
    main.o(.text) refers to fft.o(.data) for sampfre
    main.o(.text) refers to strcmpv7m.o(.text) for strcmp
    main.o(.text) refers to dac.o(.data) for dac_output_enabled
    main.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_16
    main.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable16
    main.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_32
    main.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable32
    main.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_64
    main.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable64
    main.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_128
    main.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable128
    main.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_256
    main.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable256
    main.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_512
    main.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable512
    main.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_1024
    main.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable1024
    main.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_2048
    main.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable2048
    main.o(.constdata) refers to arm_common_tables.o(.constdata) for twiddleCoef_4096
    main.o(.constdata) refers to arm_common_tables.o(.constdata) for armBitRevIndexTable4096
    main.o(.data) refers to main.o(.conststring) for .conststring
    system_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    startup_stm32f40_41xxx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(.text) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to dac.o(.text) for DMA1_Stream5_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to adc.o(.text) for ADC_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to timer.o(.text) for TIM3_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(.text) for USART1_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(.text) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(HEAP) for Heap_Mem
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(STACK) for Stack_Mem
    stm32f4xx_adc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_can.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_cryp.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_dac.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_dma2d.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_fsmc.o(.text) refers to stm32f4xx_fsmc.o(.constdata) for FSMC_DefaultTimingStruct
    stm32f4xx_gpio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_hash.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_hash_md5.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DeInit
    stm32f4xx_hash_sha1.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DeInit
    stm32f4xx_i2c.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_ltdc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_pwr.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_rcc.o(.text) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_rng.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_sai.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_sdio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_syscfg.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_wwdg.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    delay.o(.text) refers to misc.o(.text) for SysTick_CLKSourceConfig
    delay.o(.text) refers to delay.o(.data) for fac_us
    usart.o(.rev16_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.revsh_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    usart.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    usart.o(.text) refers to stm32f4xx_usart.o(.text) for USART_Init
    usart.o(.text) refers to misc.o(.text) for NVIC_Init
    usart.o(.text) refers to usart.o(.data) for Res
    usart.o(.text) refers to usart.o(.bss) for USART_RX_BUF
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    stm32f4_key.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    stm32f4_key.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    led.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    led.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    g.o(.text) refers to stm32f4xx_usart.o(.text) for USART_SendData
    g.o(.text) refers to delay.o(.text) for delay_ms
    g.o(.text) refers to g.o(.data) for beep
    timer.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphClockCmd
    timer.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseInit
    timer.o(.text) refers to misc.o(.text) for NVIC_Init
    timer.o(.text) refers to stm32f4xx_adc.o(.text) for ADC_SoftwareStartConv
    timer.o(.text) refers to usart.o(.data) for Res
    timer.o(.text) refers to timer.o(.data) for KAISHI
    timer.o(.text) refers to main.o(.data) for adc3_user_enabled
    kalman.o(.text) refers to kalman.o(.data) for current
    kalman.o(.text) refers to kalman.o(.bss) for state
    adc.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    adc.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    adc.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    adc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    adc.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    adc.o(.text) refers to stm32f4xx_adc.o(.text) for ADC_CommonInit
    adc.o(.text) refers to misc.o(.text) for NVIC_Init
    adc.o(.text) refers to stm32f4xx_dma.o(.text) for DMA_DeInit
    adc.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_Cmd
    adc.o(.text) refers to adc.o(.bss) for buff_adc
    adc.o(.text) refers to adc.o(.data) for flag_ADC
    adc.o(.text) refers to noretval__2printf.o(.text) for __2printf
    adc.o(.text) refers to main.o(.data) for sweep_test_active
    adc.o(.text) refers to main.o(.bss) for adc1_sample_buffer
    fft.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    fft.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    fft.o(.text) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    fft.o(.text) refers to arm_cfft_radix4_f32.o(.text) for arm_cfft_radix4_f32
    fft.o(.text) refers to arm_cmplx_mag_f32.o(.text) for arm_cmplx_mag_f32
    fft.o(.text) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    fft.o(.text) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    fft.o(.text) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    fft.o(.text) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    fft.o(.text) refers to fft.o(.data) for i
    fft.o(.text) refers to fft.o(.bss) for fft_inputbuf
    fft.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    fft.o(.text) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    fft.o(.text) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    fft.o(.text) refers to kalman.o(.text) for kalman_thd
    lcd.o(.text) refers to lcd.o(.bss) for lcddev
    lcd.o(.text) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    lcd.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    lcd.o(.text) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    lcd.o(.text) refers to sys.o(.text) for sys_gpio_pin_set
    lcd.o(.text) refers to delay.o(.text) for delay_ms
    lcd.o(.text) refers to lcd_ex.o(.text) for lcd_ex_st7789_reginit
    lcd.o(.text) refers to lcd.o(.constdata) for asc2_1206
    lcd.o(.text) refers to lcd.o(.data) for g_back_color
    lcd_ex.o(.text) refers to lcd.o(.text) for lcd_wr_regno
    lcd_ex.o(.text) refers to delay.o(.text) for delay_ms
    ad9833.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    ad9833.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    ad9833.o(.text) refers to delay.o(.text) for delay_ms
    ad9833.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    ad9833.o(.text) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    ad9833.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ad9833.o(.text) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    ad9833.o(.text) refers to ad9833.o(.constdata) for .constdata
    spi.o(.text) refers to stm32f4xx_spi.o(.text) for SPI_I2S_GetFlagStatus
    spi.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphClockCmd
    spi.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    dac.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    dac.o(.text) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    dac.o(.text) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    dac.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    dac.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    dac.o(.text) refers to stm32f4xx_dac.o(.text) for DAC_SetChannel1Data
    dac.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    dac.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    dac.o(.text) refers to stm32f4xx_dma.o(.text) for DMA_DeInit
    dac.o(.text) refers to misc.o(.text) for NVIC_Init
    dac.o(.text) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    dac.o(.text) refers to main.o(.text) for GetSweepPhase2Ratio
    dac.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    dac.o(.text) refers to dac.o(.bss) for dma_buffer
    dac.o(.text) refers to dac.o(.data) for dac_output_frequency
    dac.o(.text) refers to dac.o(.constdata) for amplitude_table
    dac.o(.text) refers to main.o(.data) for sweep_phase2_completed
    dac.o(.text) refers to noretval__2printf.o(.text) for __2printf
    dac.o(.text) refers to timer.o(.text) for TIM6_DAC_Init
    dac.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_Cmd
    dac.o(.text) refers to delay.o(.text) for delay_ms
    dac.o(.text) refers to main.o(.bss) for adc3_reconstructed
    dac.o(.text) refers to dac.o(.conststring) for .conststring
    touch_1.o(.text) refers to sys.o(.text) for sys_gpio_set
    touch_1.o(.text) refers to delay.o(.text) for delay_us
    touch_1.o(.text) refers to touch_1.o(.data) for t
    touch_1.o(.text) refers to lcd.o(.bss) for lcddev
    touch_1.o(.text) refers to lcd.o(.text) for lcd_show_string
    touch_1.o(.data) refers to touch_1.o(.text) for tp_init
    arm_cfft_f32.o(.text) refers to arm_cfft_radix8_f32.o(.text) for arm_radix8_butterfly_f32
    arm_cfft_f32.o(.text) refers to arm_bitreversal2.o(. text) for arm_bitreversal_32
    arm_cfft_radix4_f32.o(.text) refers to arm_bitreversal.o(.text) for arm_bitreversal_f32
    arm_cfft_radix4_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for twiddleCoef_4096
    arm_cfft_radix4_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for armBitRevTable
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    atan2.o(i.__hardfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.__hardfp_atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.__hardfp_atan2) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan2.o(i.__hardfp_atan2) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2.o(i.__hardfp_atan2) refers to fabs.o(i.fabs) for fabs
    atan2.o(i.__hardfp_atan2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2.o(i.__hardfp_atan2) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    atan2.o(i.__hardfp_atan2) refers to _rserrno.o(.text) for __set_errno
    atan2.o(i.__hardfp_atan2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2.o(i.__hardfp_atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.____hardfp_atan2$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2_x.o(i.____hardfp_atan2$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan2_x.o(i.____hardfp_atan2$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2_x.o(i.____hardfp_atan2$lsc) refers to fabs.o(i.fabs) for fabs
    atan2_x.o(i.____hardfp_atan2$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    atan2_x.o(i.____hardfp_atan2$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    cos.o(i.__hardfp_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos.o(i.__hardfp_cos) refers to _rserrno.o(.text) for __set_errno
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos.o(i.__hardfp_cos) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos.o(i.__hardfp_cos) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos.o(i.__hardfp_cos) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos.o(i.__hardfp_cos) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    cos.o(i.__softfp_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos.o(i.__softfp_cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos.o(i.cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos.o(i.cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos_x.o(i.____hardfp_cos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_x.o(i.____hardfp_cos$lsc) refers to _rserrno.o(.text) for __set_errno
    cos_x.o(i.____hardfp_cos$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos_x.o(i.____hardfp_cos$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos_x.o(i.____hardfp_cos$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos_x.o(i.____hardfp_cos$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos_x.o(i.____hardfp_cos$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    cos_x.o(i.____softfp_cos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_x.o(i.____softfp_cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    cos_x.o(i.__cos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_x.o(i.__cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    cosf.o(i.__hardfp_cosf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf.o(i.__hardfp_cosf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf.o(i.__hardfp_cosf) refers to _rserrno.o(.text) for __set_errno
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf.o(i.__softfp_cosf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf.o(i.__softfp_cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf.o(i.cosf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf.o(i.cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf_x.o(i.____hardfp_cosf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf_x.o(i.____hardfp_cosf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf_x.o(i.____hardfp_cosf$lsc) refers to _rserrno.o(.text) for __set_errno
    cosf_x.o(i.____hardfp_cosf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf_x.o(i.____softfp_cosf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf_x.o(i.____softfp_cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    cosf_x.o(i.__cosf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf_x.o(i.__cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    fabs.o(i.__hardfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log.o(i.__hardfp_log) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log.o(i.__hardfp_log) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    log.o(i.__hardfp_log) refers to _rserrno.o(.text) for __set_errno
    log.o(i.__hardfp_log) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    log.o(i.__hardfp_log) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    log.o(i.__hardfp_log) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    log.o(i.__hardfp_log) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    log.o(i.__hardfp_log) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    log.o(i.__hardfp_log) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    log.o(i.__hardfp_log) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    log.o(i.__hardfp_log) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    log.o(i.__hardfp_log) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    log.o(i.__hardfp_log) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    log.o(i.__hardfp_log) refers to poly.o(i.__kernel_poly) for __kernel_poly
    log.o(i.__hardfp_log) refers to qnan.o(.constdata) for __mathlib_zero
    log.o(i.__hardfp_log) refers to log.o(.constdata) for .constdata
    log.o(i.__softfp_log) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log.o(i.__softfp_log) refers to log.o(i.__hardfp_log) for __hardfp_log
    log.o(i.log) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log.o(i.log) refers to log.o(i.__hardfp_log) for __hardfp_log
    log.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log_x.o(i.____hardfp_log$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log_x.o(i.____hardfp_log$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    log_x.o(i.____hardfp_log$lsc) refers to _rserrno.o(.text) for __set_errno
    log_x.o(i.____hardfp_log$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    log_x.o(i.____hardfp_log$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    log_x.o(i.____hardfp_log$lsc) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    log_x.o(i.____hardfp_log$lsc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    log_x.o(i.____hardfp_log$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    log_x.o(i.____hardfp_log$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    log_x.o(i.____hardfp_log$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    log_x.o(i.____hardfp_log$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    log_x.o(i.____hardfp_log$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    log_x.o(i.____hardfp_log$lsc) refers to log_x.o(.constdata) for .constdata
    log_x.o(i.____softfp_log$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log_x.o(i.____softfp_log$lsc) refers to log_x.o(i.____hardfp_log$lsc) for ____hardfp_log$lsc
    log_x.o(i.__log$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log_x.o(i.__log$lsc) refers to log_x.o(i.____hardfp_log$lsc) for ____hardfp_log$lsc
    log_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10f.o(i.__hardfp_log10f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10f.o(i.__hardfp_log10f) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    log10f.o(i.__hardfp_log10f) refers to _rserrno.o(.text) for __set_errno
    log10f.o(i.__hardfp_log10f) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    log10f.o(i.__hardfp_log10f) refers to funder.o(i.__mathlib_flt_divzero) for __mathlib_flt_divzero
    log10f.o(i.__hardfp_log10f) refers to log10f.o(.constdata) for .constdata
    log10f.o(i.__softfp_log10f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10f.o(i.__softfp_log10f) refers to log10f.o(i.__hardfp_log10f) for __hardfp_log10f
    log10f.o(i.log10f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10f.o(i.log10f) refers to log10f.o(i.__hardfp_log10f) for __hardfp_log10f
    log10f.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10f_x.o(i.____hardfp_log10f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10f_x.o(i.____hardfp_log10f$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    log10f_x.o(i.____hardfp_log10f$lsc) refers to _rserrno.o(.text) for __set_errno
    log10f_x.o(i.____hardfp_log10f$lsc) refers to log10f_x.o(.constdata) for .constdata
    log10f_x.o(i.____softfp_log10f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10f_x.o(i.____softfp_log10f$lsc) refers to log10f_x.o(i.____hardfp_log10f$lsc) for ____hardfp_log10f$lsc
    log10f_x.o(i.__log10f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log10f_x.o(i.__log10f$lsc) refers to log10f_x.o(i.____hardfp_log10f$lsc) for ____hardfp_log10f$lsc
    log10f_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.__hardfp_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.__hardfp_sin) refers to _rserrno.o(.text) for __set_errno
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin.o(i.__hardfp_sin) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin.o(i.__hardfp_sin) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin.o(i.__hardfp_sin) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    sin.o(i.__hardfp_sin) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin.o(i.__softfp_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.__softfp_sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin.o(i.sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin_x.o(i.____hardfp_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.____hardfp_sin$lsc) refers to _rserrno.o(.text) for __set_errno
    sin_x.o(i.____hardfp_sin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin_x.o(i.____hardfp_sin$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin_x.o(i.____hardfp_sin$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin_x.o(i.____hardfp_sin$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    sin_x.o(i.____hardfp_sin$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin_x.o(i.____softfp_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.____softfp_sin$lsc) refers to sin_x.o(i.____hardfp_sin$lsc) for ____hardfp_sin$lsc
    sin_x.o(i.__sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.__sin$lsc) refers to sin_x.o(i.____hardfp_sin$lsc) for ____hardfp_sin$lsc
    sinf.o(i.__hardfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf.o(i.__hardfp_sinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    sinf.o(i.__hardfp_sinf) refers to _rserrno.o(.text) for __set_errno
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf.o(i.__softfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__softfp_sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf.o(i.sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf_x.o(i.____hardfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____hardfp_sinf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf_x.o(i.____hardfp_sinf$lsc) refers to _rserrno.o(.text) for __set_errno
    sinf_x.o(i.____hardfp_sinf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf_x.o(i.____softfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____softfp_sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sinf_x.o(i.__sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.__sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sinh.o(i.__hardfp_sinh) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinh.o(i.__hardfp_sinh) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sinh.o(i.__hardfp_sinh) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    sinh.o(i.__hardfp_sinh) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    sinh.o(i.__hardfp_sinh) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    sinh.o(i.__hardfp_sinh) refers to fabs.o(i.fabs) for fabs
    sinh.o(i.__hardfp_sinh) refers to expm1_i.o(i.__mathlib_expm1) for __mathlib_expm1
    sinh.o(i.__hardfp_sinh) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sinh.o(i.__hardfp_sinh) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sinh.o(i.__hardfp_sinh) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    sinh.o(i.__hardfp_sinh) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sinh.o(i.__hardfp_sinh) refers to exp.o(i.exp) for exp
    sinh.o(i.__hardfp_sinh) refers to _rserrno.o(.text) for __set_errno
    sinh.o(i.__hardfp_sinh) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    sinh.o(i.__hardfp_sinh) refers to sinh.o(.constdata) for .constdata
    sinh.o(i.__softfp_sinh) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinh.o(i.__softfp_sinh) refers to sinh.o(i.__hardfp_sinh) for __hardfp_sinh
    sinh.o(i.sinh) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinh.o(i.sinh) refers to sinh.o(i.__hardfp_sinh) for __hardfp_sinh
    sinh.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinh_x.o(i.____hardfp_sinh$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinh_x.o(i.____hardfp_sinh$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sinh_x.o(i.____hardfp_sinh$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    sinh_x.o(i.____hardfp_sinh$lsc) refers to fabs.o(i.fabs) for fabs
    sinh_x.o(i.____hardfp_sinh$lsc) refers to expm1_i.o(i.__mathlib_expm1) for __mathlib_expm1
    sinh_x.o(i.____hardfp_sinh$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sinh_x.o(i.____hardfp_sinh$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sinh_x.o(i.____hardfp_sinh$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    sinh_x.o(i.____hardfp_sinh$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sinh_x.o(i.____hardfp_sinh$lsc) refers to exp.o(i.exp) for exp
    sinh_x.o(i.____hardfp_sinh$lsc) refers to _rserrno.o(.text) for __set_errno
    sinh_x.o(i.____hardfp_sinh$lsc) refers to sinh_x.o(.constdata) for .constdata
    sinh_x.o(i.____softfp_sinh$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinh_x.o(i.____softfp_sinh$lsc) refers to sinh_x.o(i.____hardfp_sinh$lsc) for ____hardfp_sinh$lsc
    sinh_x.o(i.__sinh$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinh_x.o(i.__sinh$lsc) refers to sinh_x.o(i.____hardfp_sinh$lsc) for ____hardfp_sinh$lsc
    sinh_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrtf.o(i.__hardfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.__sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.__sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_char_common.o(.text) refers to __printf_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(.text) for fputc
    basic.o(x$fpl$basic) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__hardfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan.o(i.__hardfp_atan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan.o(i.__hardfp_atan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan.o(i.__hardfp_atan) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.____hardfp_atan$lsc) refers to fabs.o(i.fabs) for fabs
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan_x.o(i.____hardfp_atan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan_x.o(i.____hardfp_atan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan_x.o(i.____hardfp_atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan_x.o(i.____hardfp_atan$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan_x.o(i.____hardfp_atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    cos_i.o(i.__kernel_cos) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    cos_i.o(i.__kernel_cos) refers to poly.o(i.__kernel_poly) for __kernel_poly
    cos_i.o(i.__kernel_cos) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    cos_i.o(i.__kernel_cos) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    cos_i.o(i.__kernel_cos) refers to cos_i.o(.constdata) for .constdata
    cos_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    exp.o(i.__hardfp_exp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    exp.o(i.__hardfp_exp) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    exp.o(i.__hardfp_exp) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    exp.o(i.__hardfp_exp) refers to _rserrno.o(.text) for __set_errno
    exp.o(i.__hardfp_exp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    exp.o(i.__hardfp_exp) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    exp.o(i.__hardfp_exp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    exp.o(i.__hardfp_exp) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    exp.o(i.__hardfp_exp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    exp.o(i.__hardfp_exp) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    exp.o(i.__hardfp_exp) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    exp.o(i.__hardfp_exp) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    exp.o(i.__hardfp_exp) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    exp.o(i.__hardfp_exp) refers to poly.o(i.__kernel_poly) for __kernel_poly
    exp.o(i.__hardfp_exp) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    exp.o(i.__hardfp_exp) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    exp.o(i.__hardfp_exp) refers to exp.o(.constdata) for .constdata
    exp.o(i.__softfp_exp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    exp.o(i.__softfp_exp) refers to exp.o(i.__hardfp_exp) for __hardfp_exp
    exp.o(i.exp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    exp.o(i.exp) refers to exp.o(i.__hardfp_exp) for __hardfp_exp
    exp.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    exp_x.o(i.____hardfp_exp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    exp_x.o(i.____hardfp_exp$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    exp_x.o(i.____hardfp_exp$lsc) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    exp_x.o(i.____hardfp_exp$lsc) refers to _rserrno.o(.text) for __set_errno
    exp_x.o(i.____hardfp_exp$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    exp_x.o(i.____hardfp_exp$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    exp_x.o(i.____hardfp_exp$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    exp_x.o(i.____hardfp_exp$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    exp_x.o(i.____hardfp_exp$lsc) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    exp_x.o(i.____hardfp_exp$lsc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    exp_x.o(i.____hardfp_exp$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    exp_x.o(i.____hardfp_exp$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    exp_x.o(i.____hardfp_exp$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    exp_x.o(i.____hardfp_exp$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    exp_x.o(i.____hardfp_exp$lsc) refers to exp_x.o(.constdata) for .constdata
    exp_x.o(i.____softfp_exp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    exp_x.o(i.____softfp_exp$lsc) refers to exp_x.o(i.____hardfp_exp$lsc) for ____hardfp_exp$lsc
    exp_x.o(i.__exp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    exp_x.o(i.__exp$lsc) refers to exp_x.o(i.____hardfp_exp$lsc) for ____hardfp_exp$lsc
    exp_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    expm1_i.o(i.__mathlib_expm1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    expm1_i.o(i.__mathlib_expm1) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    expm1_i.o(i.__mathlib_expm1) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    expm1_i.o(i.__mathlib_expm1) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    expm1_i.o(i.__mathlib_expm1) refers to _rserrno.o(.text) for __set_errno
    expm1_i.o(i.__mathlib_expm1) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    expm1_i.o(i.__mathlib_expm1) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    expm1_i.o(i.__mathlib_expm1) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    expm1_i.o(i.__mathlib_expm1) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    expm1_i.o(i.__mathlib_expm1) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    expm1_i.o(i.__mathlib_expm1) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    expm1_i.o(i.__mathlib_expm1) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    expm1_i.o(i.__mathlib_expm1) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    expm1_i.o(i.__mathlib_expm1) refers to poly.o(i.__kernel_poly) for __kernel_poly
    expm1_i.o(i.__mathlib_expm1) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    expm1_i.o(i.__mathlib_expm1) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    expm1_i.o(i.__mathlib_expm1) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    expm1_i.o(i.__mathlib_expm1) refers to expm1_i.o(.constdata) for .constdata
    expm1_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    expm1_i_x.o(i.____mathlib_expm1$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    expm1_i_x.o(i.____mathlib_expm1$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    expm1_i_x.o(i.____mathlib_expm1$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    expm1_i_x.o(i.____mathlib_expm1$lsc) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    expm1_i_x.o(i.____mathlib_expm1$lsc) refers to _rserrno.o(.text) for __set_errno
    expm1_i_x.o(i.____mathlib_expm1$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    expm1_i_x.o(i.____mathlib_expm1$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    expm1_i_x.o(i.____mathlib_expm1$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    expm1_i_x.o(i.____mathlib_expm1$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    expm1_i_x.o(i.____mathlib_expm1$lsc) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    expm1_i_x.o(i.____mathlib_expm1$lsc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    expm1_i_x.o(i.____mathlib_expm1$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    expm1_i_x.o(i.____mathlib_expm1$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    expm1_i_x.o(i.____mathlib_expm1$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    expm1_i_x.o(i.____mathlib_expm1$lsc) refers to expm1_i_x.o(.constdata) for .constdata
    expm1_i_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    rred.o(i.__ieee754_rem_pio2) refers to fabs.o(i.fabs) for fabs
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    rred.o(i.__ieee754_rem_pio2) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    sin_i.o(i.__kernel_sin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    sin_i.o(i.__kernel_sin) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sin_i.o(i.__kernel_sin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sin_i.o(i.__kernel_sin) refers to sin_i.o(.constdata) for .constdata
    sin_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sin_i_x.o(i.____kernel_sin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sin_i_x.o(i.____kernel_sin$lsc) refers to sin_i_x.o(.constdata) for .constdata
    sin_i_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f40_41xxx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_can.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(.text), (2544 bytes).
    Removing stm32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_crc.o(.text), (72 bytes).
    Removing stm32f4xx_cryp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.text), (856 bytes).
    Removing stm32f4xx_cryp_aes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.text), (4586 bytes).
    Removing stm32f4xx_cryp_des.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.text), (472 bytes).
    Removing stm32f4xx_cryp_tdes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.text), (536 bytes).
    Removing stm32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.text), (100 bytes).
    Removing stm32f4xx_dcmi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.text), (396 bytes).
    Removing stm32f4xx_dma2d.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.text), (948 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_exti.o(.text), (272 bytes).
    Removing stm32f4xx_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash.o(.text), (1684 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.text), (64 bytes).
    Removing stm32f4xx_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.text), (1480 bytes).
    Removing stm32f4xx_fsmc.o(.constdata), (28 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.text), (552 bytes).
    Removing stm32f4xx_hash_md5.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.text), (534 bytes).
    Removing stm32f4xx_hash_sha1.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.text), (548 bytes).
    Removing stm32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.text), (1110 bytes).
    Removing stm32f4xx_iwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.text), (64 bytes).
    Removing stm32f4xx_ltdc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.text), (1672 bytes).
    Removing stm32f4xx_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.text), (364 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rng.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.text), (160 bytes).
    Removing stm32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.text), (3432 bytes).
    Removing stm32f4xx_sai.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sai.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sai.o(.text), (524 bytes).
    Removing stm32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.text), (476 bytes).
    Removing stm32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spi.o(.text), (1152 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.text), (148 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.text), (144 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.emb_text), (16 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing stm32f4_key.o(.rev16_text), (4 bytes).
    Removing stm32f4_key.o(.revsh_text), (4 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing g.o(.rev16_text), (4 bytes).
    Removing g.o(.revsh_text), (4 bytes).
    Removing g.o(.text), (148 bytes).
    Removing g.o(.data), (1 bytes).
    Removing timer.o(.rev16_text), (4 bytes).
    Removing timer.o(.revsh_text), (4 bytes).
    Removing kalman.o(.rev16_text), (4 bytes).
    Removing kalman.o(.revsh_text), (4 bytes).
    Removing kalman.o(.text), (388 bytes).
    Removing kalman.o(.bss), (308 bytes).
    Removing kalman.o(.data), (4 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing fft.o(.rev16_text), (4 bytes).
    Removing fft.o(.revsh_text), (4 bytes).
    Removing fft.o(.text), (1868 bytes).
    Removing lcd.o(.rev16_text), (4 bytes).
    Removing lcd.o(.revsh_text), (4 bytes).
    Removing lcd_ex.o(.rev16_text), (4 bytes).
    Removing lcd_ex.o(.revsh_text), (4 bytes).
    Removing ad9833.o(.rev16_text), (4 bytes).
    Removing ad9833.o(.revsh_text), (4 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.text), (280 bytes).
    Removing spi.o(.data), (4 bytes).
    Removing dac.o(.rev16_text), (4 bytes).
    Removing dac.o(.revsh_text), (4 bytes).
    Removing touch_1.o(.rev16_text), (4 bytes).
    Removing touch_1.o(.revsh_text), (4 bytes).
    Removing touch_1.o(.text), (1700 bytes).
    Removing touch_1.o(.data), (61 bytes).
    Removing arm_cmplx_mag_f32.o(.rev16_text), (4 bytes).
    Removing arm_cmplx_mag_f32.o(.revsh_text), (4 bytes).
    Removing arm_cfft_f32.o(.rev16_text), (4 bytes).
    Removing arm_cfft_f32.o(.revsh_text), (4 bytes).
    Removing arm_cfft_radix4_f32.o(.rev16_text), (4 bytes).
    Removing arm_cfft_radix4_f32.o(.revsh_text), (4 bytes).
    Removing arm_cfft_radix4_f32.o(.text), (1696 bytes).
    Removing arm_cfft_radix4_init_f32.o(.rev16_text), (4 bytes).
    Removing arm_cfft_radix4_init_f32.o(.revsh_text), (4 bytes).
    Removing arm_common_tables.o(.rev16_text), (4 bytes).
    Removing arm_common_tables.o(.revsh_text), (4 bytes).
    Removing arm_common_tables.o(.constdata), (24576 bytes).
    Removing arm_common_tables.o(.constdata), (12288 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (512 bytes).
    Removing arm_common_tables.o(.constdata), (1024 bytes).
    Removing arm_common_tables.o(.constdata), (2048 bytes).
    Removing arm_common_tables.o(.constdata), (4096 bytes).
    Removing arm_common_tables.o(.constdata), (8192 bytes).
    Removing arm_common_tables.o(.constdata), (16384 bytes).
    Removing arm_bitreversal.o(.rev16_text), (4 bytes).
    Removing arm_bitreversal.o(.revsh_text), (4 bytes).
    Removing arm_bitreversal.o(.text), (486 bytes).
    Removing arm_cfft_radix8_f32.o(.rev16_text), (4 bytes).
    Removing arm_cfft_radix8_f32.o(.revsh_text), (4 bytes).

175 unused section(s) (total 102254 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/dczerorl2.s                0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../fplib/basic.s                         0x00000000   Number         0  basic.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/deqf.s                          0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_umaal.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos_x.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos.o ABSOLUTE
    ../mathlib/cos_i.c                       0x00000000   Number         0  cos_i.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf_x.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/exp.c                         0x00000000   Number         0  exp_x.o ABSOLUTE
    ../mathlib/exp.c                         0x00000000   Number         0  exp.o ABSOLUTE
    ../mathlib/expm1_i.c                     0x00000000   Number         0  expm1_i.o ABSOLUTE
    ../mathlib/expm1_i.c                     0x00000000   Number         0  expm1_i_x.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/log.c                         0x00000000   Number         0  log.o ABSOLUTE
    ../mathlib/log.c                         0x00000000   Number         0  log_x.o ABSOLUTE
    ../mathlib/log10f.c                      0x00000000   Number         0  log10f.o ABSOLUTE
    ../mathlib/log10f.c                      0x00000000   Number         0  log10f_x.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/rred.c                        0x00000000   Number         0  rred.o ABSOLUTE
    ../mathlib/rredf.c                       0x00000000   Number         0  rredf.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin_x.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i_x.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf_x.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf.o ABSOLUTE
    ../mathlib/sinh.c                        0x00000000   Number         0  sinh_x.o ABSOLUTE
    ../mathlib/sinh.c                        0x00000000   Number         0  sinh.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf_x.o ABSOLUTE
    ..\CORE\startup_stm32f40_41xxx.s         0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    ..\CommonTables\arm_common_tables.c      0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    ..\ComplexMathFunctions\arm_cmplx_mag_f32.c 0x00000000   Number         0  arm_cmplx_mag_f32.o ABSOLUTE
    ..\FWLIB\src\misc.c                      0x00000000   Number         0  misc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_adc.c             0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_can.c             0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_crc.c             0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp.c            0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_aes.c        0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_des.c        0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_tdes.c       0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dac.c             0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dbgmcu.c          0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dcmi.c            0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dma.c             0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dma2d.c           0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_exti.c            0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_flash.c           0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_flash_ramfunc.c   0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_fsmc.c            0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_gpio.c            0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash.c            0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash_md5.c        0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash_sha1.c       0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_i2c.c             0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_iwdg.c            0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_ltdc.c            0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_pwr.c             0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rcc.c             0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rng.c             0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rtc.c             0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_sai.c             0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_sdio.c            0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_spi.c             0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_syscfg.c          0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_tim.c             0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_usart.c           0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_wwdg.c            0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\HARDWARE\AD9833\AD9833.c              0x00000000   Number         0  ad9833.o ABSOLUTE
    ..\HARDWARE\AD9833\spi.c                 0x00000000   Number         0  spi.o ABSOLUTE
    ..\HARDWARE\ADC\adc.c                    0x00000000   Number         0  adc.o ABSOLUTE
    ..\HARDWARE\DAC\dac.c                    0x00000000   Number         0  dac.o ABSOLUTE
    ..\HARDWARE\FFT\fft.c                    0x00000000   Number         0  fft.o ABSOLUTE
    ..\HARDWARE\G\G.c                        0x00000000   Number         0  g.o ABSOLUTE
    ..\HARDWARE\KALMAN\kalman.c              0x00000000   Number         0  kalman.o ABSOLUTE
    ..\HARDWARE\LCD\lcd.c                    0x00000000   Number         0  lcd.o ABSOLUTE
    ..\HARDWARE\LCD\lcd_ex.c                 0x00000000   Number         0  lcd_ex.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\HARDWARE\TIMER2\timer.c               0x00000000   Number         0  timer.o ABSOLUTE
    ..\HARDWARE\TOUCH\touch.c                0x00000000   Number         0  touch_1.o ABSOLUTE
    ..\HARDWARE\key\stm32f4_key.c            0x00000000   Number         0  stm32f4_key.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\TransformFunctions\arm_bitreversal.c  0x00000000   Number         0  arm_bitreversal.o ABSOLUTE
    ..\TransformFunctions\arm_cfft_f32.c     0x00000000   Number         0  arm_cfft_f32.o ABSOLUTE
    ..\TransformFunctions\arm_cfft_radix4_f32.c 0x00000000   Number         0  arm_cfft_radix4_f32.o ABSOLUTE
    ..\TransformFunctions\arm_cfft_radix4_init_f32.c 0x00000000   Number         0  arm_cfft_radix4_init_f32.o ABSOLUTE
    ..\TransformFunctions\arm_cfft_radix8_f32.c 0x00000000   Number         0  arm_cfft_radix8_f32.o ABSOLUTE
    ..\\CommonTables\\arm_common_tables.c    0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    ..\\ComplexMathFunctions\\arm_cmplx_mag_f32.c 0x00000000   Number         0  arm_cmplx_mag_f32.o ABSOLUTE
    ..\\FWLIB\\src\\misc.c                   0x00000000   Number         0  misc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_adc.c          0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_can.c          0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_crc.c          0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp.c         0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_aes.c     0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_des.c     0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_tdes.c    0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dac.c          0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dbgmcu.c       0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dcmi.c         0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dma.c          0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dma2d.c        0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_exti.c         0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_flash.c        0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_fsmc.c         0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_gpio.c         0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash.c         0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash_md5.c     0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash_sha1.c    0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_i2c.c          0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_iwdg.c         0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_ltdc.c         0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_pwr.c          0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rcc.c          0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rng.c          0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rtc.c          0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_sai.c          0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_sdio.c         0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_spi.c          0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_syscfg.c       0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_tim.c          0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_usart.c        0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_wwdg.c         0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\\HARDWARE\\AD9833\\AD9833.c           0x00000000   Number         0  ad9833.o ABSOLUTE
    ..\\HARDWARE\\AD9833\\spi.c              0x00000000   Number         0  spi.o ABSOLUTE
    ..\\HARDWARE\\ADC\\adc.c                 0x00000000   Number         0  adc.o ABSOLUTE
    ..\\HARDWARE\\DAC\\dac.c                 0x00000000   Number         0  dac.o ABSOLUTE
    ..\\HARDWARE\\FFT\\fft.c                 0x00000000   Number         0  fft.o ABSOLUTE
    ..\\HARDWARE\\G\\G.c                     0x00000000   Number         0  g.o ABSOLUTE
    ..\\HARDWARE\\KALMAN\\kalman.c           0x00000000   Number         0  kalman.o ABSOLUTE
    ..\\HARDWARE\\LCD\\lcd.c                 0x00000000   Number         0  lcd.o ABSOLUTE
    ..\\HARDWARE\\LCD\\lcd_ex.c              0x00000000   Number         0  lcd_ex.o ABSOLUTE
    ..\\HARDWARE\\LED\\led.c                 0x00000000   Number         0  led.o ABSOLUTE
    ..\\HARDWARE\\TIMER2\\timer.c            0x00000000   Number         0  timer.o ABSOLUTE
    ..\\HARDWARE\\TOUCH\\touch.c             0x00000000   Number         0  touch_1.o ABSOLUTE
    ..\\HARDWARE\\key\\stm32f4_key.c         0x00000000   Number         0  stm32f4_key.o ABSOLUTE
    ..\\SYSTEM\\delay\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    ..\\SYSTEM\\usart\\usart.c               0x00000000   Number         0  usart.o ABSOLUTE
    ..\\TransformFunctions\\arm_bitreversal.c 0x00000000   Number         0  arm_bitreversal.o ABSOLUTE
    ..\\TransformFunctions\\arm_bitreversal2.S 0x00000000   Number         0  arm_bitreversal2.o ABSOLUTE
    ..\\TransformFunctions\\arm_cfft_f32.c   0x00000000   Number         0  arm_cfft_f32.o ABSOLUTE
    ..\\TransformFunctions\\arm_cfft_radix4_f32.c 0x00000000   Number         0  arm_cfft_radix4_f32.o ABSOLUTE
    ..\\TransformFunctions\\arm_cfft_radix4_init_f32.c 0x00000000   Number         0  arm_cfft_radix4_init_f32.o ABSOLUTE
    ..\\TransformFunctions\\arm_cfft_radix8_f32.c 0x00000000   Number         0  arm_cfft_radix8_f32.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!dczerorl2                              0x080001c4   Section       90  __dczerorl2.o(!!dczerorl2)
    !!handler_zi                             0x08000220   Section       28  __scatter_zi.o(!!handler_zi)
    . text                                   0x0800023c   Section      106  arm_bitreversal2.o(. text)
    $v0                                      0x0800023c   Number         0  arm_bitreversal2.o(. text)
    .ARM.Collect$$_printf_percent$$00000000  0x080002a6   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x080002a6   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000009  0x080002ac   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$00000014  0x080002b2   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x080002b8   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080002bc   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x080002be   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x080002c2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080002c2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080002c2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080002c2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x080002c2   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x080002c8   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080002c8   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080002c8   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x080002c8   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080002d2   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080002d4   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080002d6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080002d6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x080002d6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080002d6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x080002d6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x080002d6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x080002d6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x080002d6   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x080002d8   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080002d8   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080002d8   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080002de   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080002de   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080002e2   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080002e2   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080002ea   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080002ec   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080002ec   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080002f0   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080002f8   Section        0  main.o(.text)
    .text                                    0x0800b68c   Section        0  stm32f4xx_it.o(.text)
    .text                                    0x0800b6a8   Section        0  system_stm32f4xx.o(.text)
    SetSysClock                              0x0800b6a9   Thumb Code   220  system_stm32f4xx.o(.text)
    .text                                    0x0800b8b8   Section       64  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x0800b8b8   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x0800b8f8   Section        0  misc.o(.text)
    .text                                    0x0800b9d8   Section        0  stm32f4xx_adc.o(.text)
    .text                                    0x0800be3c   Section        0  stm32f4xx_dac.o(.text)
    .text                                    0x0800c04c   Section        0  stm32f4xx_dma.o(.text)
    .text                                    0x0800c3f4   Section        0  stm32f4xx_gpio.o(.text)
    .text                                    0x0800c688   Section        0  stm32f4xx_rcc.o(.text)
    .text                                    0x0800cce4   Section        0  stm32f4xx_tim.o(.text)
    TI4_Config                               0x0800d483   Thumb Code    80  stm32f4xx_tim.o(.text)
    TI3_Config                               0x0800d4e5   Thumb Code    72  stm32f4xx_tim.o(.text)
    TI2_Config                               0x0800d547   Thumb Code    90  stm32f4xx_tim.o(.text)
    TI1_Config                               0x0800d5b3   Thumb Code    58  stm32f4xx_tim.o(.text)
    .text                                    0x0800d988   Section        0  stm32f4xx_usart.o(.text)
    .text                                    0x0800dddc   Section        0  delay.o(.text)
    .text                                    0x0800dee0   Section        0  sys.o(.text)
    sys_nvic_priority_group_config           0x0800deed   Thumb Code    36  sys.o(.text)
    .text                                    0x0800e458   Section        0  usart.o(.text)
    .text                                    0x0800e5b4   Section        0  stm32f4_key.o(.text)
    .text                                    0x0800e7e8   Section        0  led.o(.text)
    .text                                    0x0800e828   Section        0  timer.o(.text)
    .text                                    0x0800ea24   Section        0  adc.o(.text)
    .text                                    0x0800f12c   Section        0  lcd.o(.text)
    lcd_opt_delay                            0x0800f163   Thumb Code    12  lcd.o(.text)
    lcd_rd_data                              0x0800f16f   Thumb Code    20  lcd.o(.text)
    lcd_pow                                  0x0801037b   Thumb Code    22  lcd.o(.text)
    .text                                    0x08010570   Section        0  lcd_ex.o(.text)
    .text                                    0x08012e18   Section        0  ad9833.o(.text)
    .text                                    0x08013174   Section        0  dac.o(.text)
    DAC_GenerateSineTable                    0x080132c7   Thumb Code   148  dac.o(.text)
    DAC_UpdateDMABuffer                      0x0801335b   Thumb Code   222  dac.o(.text)
    DAC_UpdateTimerFrequency                 0x0801366d   Thumb Code   236  dac.o(.text)
    .text                                    0x080143d0   Section        0  arm_cmplx_mag_f32.o(.text)
    .text                                    0x080144c0   Section        0  arm_cfft_f32.o(.text)
    .text                                    0x08014b98   Section        0  arm_cfft_radix4_init_f32.o(.text)
    .text                                    0x08014c58   Section        0  arm_cfft_radix8_f32.o(.text)
    .text                                    0x08015134   Section        2  use_no_semi_2.o(.text)
    .text                                    0x08015138   Section        0  noretval__2printf.o(.text)
    .text                                    0x08015150   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x08015178   Section        0  _printf_str.o(.text)
    .text                                    0x080151cc   Section        0  _printf_dec.o(.text)
    .text                                    0x08015244   Section        0  __printf_wp.o(.text)
    .text                                    0x08015352   Section        0  strlen.o(.text)
    .text                                    0x08015390   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x080153f4   Section      128  strcmpv7m.o(.text)
    .text                                    0x08015474   Section        0  heapauxi.o(.text)
    .text                                    0x0801547a   Section        2  use_no_semi.o(.text)
    .text                                    0x0801547c   Section        0  _rserrno.o(.text)
    .text                                    0x08015492   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08015544   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08015547   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08015964   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08015965   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08015994   Section        0  _sputc.o(.text)
    .text                                    0x0801599e   Section        0  _printf_char.o(.text)
    .text                                    0x080159cc   Section        0  _printf_char_file.o(.text)
    .text                                    0x080159f0   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x080159f8   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08015a00   Section      138  lludiv10.o(.text)
    .text                                    0x08015a8c   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08015b0c   Section        0  bigflt0.o(.text)
    .text                                    0x08015bf0   Section        0  ferror.o(.text)
    .text                                    0x08015bf8   Section        8  libspace.o(.text)
    .text                                    0x08015c00   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08015c4a   Section        0  exit.o(.text)
    CL$$btod_d2e                             0x08015c5c   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08015c9a   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08015ce0   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08015d40   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08016078   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08016154   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x0801617e   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x080161a8   Section      580  btod.o(CL$$btod_mult_common)
    i.__ARM_fpclassify                       0x080163ec   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__ARM_fpclassifyf                      0x0801641c   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__hardfp_atan                          0x08016448   Section        0  atan.o(i.__hardfp_atan)
    i.__hardfp_atan2                         0x08016720   Section        0  atan2.o(i.__hardfp_atan2)
    i.__hardfp_cosf                          0x08016920   Section        0  cosf.o(i.__hardfp_cosf)
    i.__hardfp_exp                           0x08016a70   Section        0  exp.o(i.__hardfp_exp)
    i.__hardfp_fabs                          0x08016dc8   Section        0  fabs.o(i.__hardfp_fabs)
    i.__hardfp_log                           0x08016de0   Section        0  log.o(i.__hardfp_log)
    i.__hardfp_log10f                        0x080171a4   Section        0  log10f.o(i.__hardfp_log10f)
    i.__hardfp_sinf                          0x08017324   Section        0  sinf.o(i.__hardfp_sinf)
    i.__hardfp_sinh                          0x080174b8   Section        0  sinh.o(i.__hardfp_sinh)
    i.__hardfp_sqrt                          0x080176a8   Section        0  sqrt.o(i.__hardfp_sqrt)
    i.__hardfp_sqrtf                         0x08017722   Section        0  sqrtf.o(i.__hardfp_sqrtf)
    i.__kernel_poly                          0x0801775c   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_divzero                  0x08017858   Section        0  dunder.o(i.__mathlib_dbl_divzero)
    i.__mathlib_dbl_infnan                   0x08017888   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_infnan2                  0x0801789c   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x080178b0   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_overflow                 0x080178d0   Section        0  dunder.o(i.__mathlib_dbl_overflow)
    i.__mathlib_dbl_underflow                0x080178f0   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__mathlib_expm1                        0x08017910   Section        0  expm1_i.o(i.__mathlib_expm1)
    i.__mathlib_flt_divzero                  0x08017e04   Section        0  funder.o(i.__mathlib_flt_divzero)
    i.__mathlib_flt_infnan                   0x08017e18   Section        0  funder.o(i.__mathlib_flt_infnan)
    i.__mathlib_flt_invalid                  0x08017e20   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.__mathlib_flt_underflow                0x08017e30   Section        0  funder.o(i.__mathlib_flt_underflow)
    i.__mathlib_rredf2                       0x08017e40   Section        0  rredf.o(i.__mathlib_rredf2)
    i._is_digit                              0x08017f94   Section        0  __printf_wp.o(i._is_digit)
    i.atan                                   0x08017fa2   Section        0  atan.o(i.atan)
    i.exp                                    0x08017fb2   Section        0  exp.o(i.exp)
    i.fabs                                   0x08017fc2   Section        0  fabs.o(i.fabs)
    locale$$code                             0x08017fdc   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$basic                              0x08018008   Section       24  basic.o(x$fpl$basic)
    $v0                                      0x08018008   Number         0  basic.o(x$fpl$basic)
    x$fpl$d2f                                0x08018020   Section       98  d2f.o(x$fpl$d2f)
    $v0                                      0x08018020   Number         0  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x08018084   Section      336  daddsub_clz.o(x$fpl$dadd)
    $v0                                      0x08018084   Number         0  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x08018095   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcmpinf                            0x080181d4   Section       24  dcmpi.o(x$fpl$dcmpinf)
    $v0                                      0x080181d4   Number         0  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$ddiv                               0x080181ec   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x080181ec   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x080181f3   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$deqf                               0x0801849c   Section      120  deqf.o(x$fpl$deqf)
    $v0                                      0x0801849c   Number         0  deqf.o(x$fpl$deqf)
    x$fpl$dfix                               0x08018514   Section       94  dfix.o(x$fpl$dfix)
    $v0                                      0x08018514   Number         0  dfix.o(x$fpl$dfix)
    x$fpl$dfixu                              0x08018574   Section       90  dfixu.o(x$fpl$dfixu)
    $v0                                      0x08018574   Number         0  dfixu.o(x$fpl$dfixu)
    x$fpl$dflt                               0x080185ce   Section       46  dflt_clz.o(x$fpl$dflt)
    $v0                                      0x080185ce   Number         0  dflt_clz.o(x$fpl$dflt)
    x$fpl$dfltu                              0x080185fc   Section       38  dflt_clz.o(x$fpl$dfltu)
    $v0                                      0x080185fc   Number         0  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dleqf                              0x08018624   Section      120  dleqf.o(x$fpl$dleqf)
    $v0                                      0x08018624   Number         0  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x0801869c   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x0801869c   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x080187f0   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x080187f0   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x0801888c   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x0801888c   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x08018898   Section      108  drleqf.o(x$fpl$drleqf)
    $v0                                      0x08018898   Number         0  drleqf.o(x$fpl$drleqf)
    x$fpl$drsb                               0x08018904   Section       22  daddsub_clz.o(x$fpl$drsb)
    $v0                                      0x08018904   Number         0  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsqrt                              0x0801891c   Section      408  dsqrt_umaal.o(x$fpl$dsqrt)
    $v0                                      0x0801891c   Number         0  dsqrt_umaal.o(x$fpl$dsqrt)
    x$fpl$dsub                               0x08018ab4   Section      468  daddsub_clz.o(x$fpl$dsub)
    $v0                                      0x08018ab4   Number         0  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x08018ac5   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x08018c88   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x08018c88   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x08018cde   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x08018cde   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x08018d6a   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x08018d6a   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fretinf                            0x08018d74   Section       10  fretinf.o(x$fpl$fretinf)
    $v0                                      0x08018d74   Number         0  fretinf.o(x$fpl$fretinf)
    x$fpl$printf1                            0x08018d7e   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x08018d7e   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$usenofp                            0x08018d82   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x08018d84   Section      194  main.o(.constdata)
    .constdata                               0x08018e46   Section    12160  lcd.o(.constdata)
    .constdata                               0x0801bdc8   Section        5  ad9833.o(.constdata)
    .constdata                               0x0801bdd0   Section      120  dac.o(.constdata)
    amplitude_table                          0x0801bdd0   Data         120  dac.o(.constdata)
    .constdata                               0x0801be48   Section     2048  arm_common_tables.o(.constdata)
    .constdata                               0x0801c648   Section      128  arm_common_tables.o(.constdata)
    .constdata                               0x0801c6c8   Section      256  arm_common_tables.o(.constdata)
    .constdata                               0x0801c7c8   Section      512  arm_common_tables.o(.constdata)
    .constdata                               0x0801c9c8   Section     1024  arm_common_tables.o(.constdata)
    .constdata                               0x0801cdc8   Section     2048  arm_common_tables.o(.constdata)
    .constdata                               0x0801d5c8   Section     4096  arm_common_tables.o(.constdata)
    .constdata                               0x0801e5c8   Section     8192  arm_common_tables.o(.constdata)
    .constdata                               0x080205c8   Section    16384  arm_common_tables.o(.constdata)
    .constdata                               0x080245c8   Section    32768  arm_common_tables.o(.constdata)
    .constdata                               0x0802c5c8   Section       40  arm_common_tables.o(.constdata)
    .constdata                               0x0802c5f0   Section       96  arm_common_tables.o(.constdata)
    .constdata                               0x0802c650   Section      112  arm_common_tables.o(.constdata)
    .constdata                               0x0802c6c0   Section      416  arm_common_tables.o(.constdata)
    .constdata                               0x0802c860   Section      880  arm_common_tables.o(.constdata)
    .constdata                               0x0802cbd0   Section      896  arm_common_tables.o(.constdata)
    .constdata                               0x0802cf50   Section     3600  arm_common_tables.o(.constdata)
    .constdata                               0x0802dd60   Section     7616  arm_common_tables.o(.constdata)
    .constdata                               0x0802fb20   Section     8064  arm_common_tables.o(.constdata)
    .constdata                               0x08031aa0   Section       56  log.o(.constdata)
    Lg2                                      0x08031aa0   Data          24  log.o(.constdata)
    Lg                                       0x08031ab8   Data          32  log.o(.constdata)
    .constdata                               0x08031ad8   Section       64  log10f.o(.constdata)
    logahi                                   0x08031ad8   Data          32  log10f.o(.constdata)
    logalo                                   0x08031af8   Data          32  log10f.o(.constdata)
    .constdata                               0x08031b18   Section        8  sinh.o(.constdata)
    one                                      0x08031b18   Data           8  sinh.o(.constdata)
    .constdata                               0x08031b20   Section      152  atan.o(.constdata)
    atanhi                                   0x08031b20   Data          32  atan.o(.constdata)
    atanlo                                   0x08031b40   Data          32  atan.o(.constdata)
    aTodd                                    0x08031b60   Data          40  atan.o(.constdata)
    aTeven                                   0x08031b88   Data          48  atan.o(.constdata)
    .constdata                               0x08031bb8   Section       88  exp.o(.constdata)
    halF                                     0x08031bb8   Data          16  exp.o(.constdata)
    ln2HI                                    0x08031bc8   Data          16  exp.o(.constdata)
    ln2LO                                    0x08031bd8   Data          16  exp.o(.constdata)
    P                                        0x08031be8   Data          40  exp.o(.constdata)
    .constdata                               0x08031c10   Section       40  expm1_i.o(.constdata)
    Q                                        0x08031c10   Data          40  expm1_i.o(.constdata)
    .constdata                               0x08031c38   Section        8  qnan.o(.constdata)
    .constdata                               0x08031c40   Section       32  rredf.o(.constdata)
    twooverpi                                0x08031c40   Data          32  rredf.o(.constdata)
    .constdata                               0x08031c60   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x08031c60   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x08031c9c   Data          64  bigflt0.o(.constdata)
    .conststring                             0x08031cf4   Section     3414  main.o(.conststring)
    .conststring                             0x08032a4c   Section       72  dac.o(.conststring)
    locale$$data                             0x08032ab4   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x08032ab8   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08032ac0   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x08032acc   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x08032ace   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x08032acf   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x08032ad0   Data           0  lc_numeric_c.o(locale$$data)
    .data                                    0x20000000   Section      384  main.o(.data)
    save_counter                             0x2000017c   Data           4  main.o(.data)
    .data                                    0x20000180   Section       20  system_stm32f4xx.o(.data)
    .data                                    0x20000194   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x20000194   Data          16  stm32f4xx_rcc.o(.data)
    .data                                    0x200001a4   Section        4  delay.o(.data)
    fac_us                                   0x200001a4   Data           1  delay.o(.data)
    fac_ms                                   0x200001a6   Data           2  delay.o(.data)
    .data                                    0x200001a8   Section       10  usart.o(.data)
    .data                                    0x200001b4   Section        4  timer.o(.data)
    .data                                    0x200001b8   Section       39  adc.o(.data)
    .data                                    0x200001e0   Section       60  fft.o(.data)
    i                                        0x2000021a   Data           2  fft.o(.data)
    .data                                    0x2000021c   Section        8  lcd.o(.data)
    .data                                    0x20000224   Section       21  dac.o(.data)
    current_dma_buffer_size                  0x20000224   Data           4  dac.o(.data)
    current_amplitude                        0x20000234   Data           4  dac.o(.data)
    tim6_initialized                         0x20000238   Data           1  dac.o(.data)
    .bss                                     0x2000023c   Section    22928  main.o(.bss)
    .bss                                     0x20005bcc   Section      200  usart.o(.bss)
    .bss                                     0x20005c94   Section    24576  adc.o(.bss)
    .bss                                     0x2000bc94   Section    65680  fft.o(.bss)
    .bss                                     0x2001bd24   Section       14  lcd.o(.bss)
    .bss                                     0x2001bd32   Section      512  dac.o(.bss)
    sine_table                               0x2001bd32   Data         256  dac.o(.bss)
    dma_buffer                               0x2001be32   Data         256  dac.o(.bss)
    .bss                                     0x2001bf34   Section       96  libspace.o(.bss)
    HEAP                                     0x2001bf98   Section      512  startup_stm32f40_41xxx.o(HEAP)
    Heap_Mem                                 0x2001bf98   Data         512  startup_stm32f40_41xxx.o(HEAP)
    STACK                                    0x2001c198   Section     1024  startup_stm32f40_41xxx.o(STACK)
    Stack_Mem                                0x2001c198   Data        1024  startup_stm32f40_41xxx.o(STACK)
    __initial_sp                             0x2001c598   Data           0  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x080001c5   Thumb Code    90  __dczerorl2.o(!!dczerorl2)
    __decompress1                            0x080001c5   Thumb Code     0  __dczerorl2.o(!!dczerorl2)
    __scatterload_zeroinit                   0x08000221   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    arm_bitreversal_32                       0x0800023d   Thumb Code   106  arm_bitreversal2.o(. text)
    _printf_f                                0x080002a7   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x080002a7   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x080002ad   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_s                                0x080002b3   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x080002b9   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080002bd   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080002bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x080002c3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x080002c3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x080002c3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080002c3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x080002c3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x080002c9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x080002c9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080002c9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x080002c9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x080002d5   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080002d7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x080002d7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080002d7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x080002d7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x080002d7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x080002d7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x080002d7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x080002d7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x080002d9   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080002d9   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080002d9   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080002df   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080002df   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080002e3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080002e3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080002eb   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080002ed   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080002ed   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080002f1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    format_frequency_display                 0x080002f9   Thumb Code   142  main.o(.text)
    draw_button                              0x08000387   Thumb Code   306  main.o(.text)
    draw_all_buttons                         0x080004b9   Thumb Code    44  main.o(.text)
    check_button_press                       0x080004e5   Thumb Code   104  main.o(.text)
    adjust_frequency                         0x0800054d   Thumb Code   122  main.o(.text)
    ADC3_StartSampling                       0x080005c7   Thumb Code    50  main.o(.text)
    ADC3_ResetSampling                       0x080005f9   Thumb Code    32  main.o(.text)
    ADC3_ReconstructPureSineWave             0x08000619   Thumb Code  2882  main.o(.text)
    GetSweepPhase2Ratio                      0x0800115b   Thumb Code   172  main.o(.text)
    ADC3_ReconstructComplexWaveform          0x08001207   Thumb Code  4474  main.o(.text)
    ADC3_GetSweepRatio                       0x08002381   Thumb Code   648  main.o(.text)
    ADC3_ApplySweepCorrection                0x08002609   Thumb Code   396  main.o(.text)
    ADC3_ReconstructStandardSineWave_2VPP    0x08002795   Thumb Code  1574  main.o(.text)
    ADC3_IntelligentReconstruction           0x08002dbb   Thumb Code  1798  main.o(.text)
    ADC3_CalculateOptimalDACParams           0x080034c1   Thumb Code  1068  main.o(.text)
    ADC3_OutputFFTResults                    0x080038ed   Thumb Code  1562  main.o(.text)
    ADC3_ProcessFFT                          0x08003f07   Thumb Code  2258  main.o(.text)
    ProcessIIRFilter                         0x080047d9   Thumb Code    90  main.o(.text)
    ADC3_ProcessIIRFilter                    0x08004833   Thumb Code  1338  main.o(.text)
    DetermineFilterType                      0x08004d6d   Thumb Code  1340  main.o(.text)
    OutputSweepResults                       0x080052a9   Thumb Code     2  main.o(.text)
    ADC2_StopSampling                        0x080052ab   Thumb Code    12  main.o(.text)
    ADC1_StopSampling                        0x080052b7   Thumb Code    12  main.o(.text)
    StopSweepTest                            0x080052c3   Thumb Code    18  main.o(.text)
    ADC2_StartSampling                       0x080052d5   Thumb Code    50  main.o(.text)
    ADC1_StartSampling                       0x08005307   Thumb Code    50  main.o(.text)
    ADC2_ResetSampling                       0x08005339   Thumb Code    32  main.o(.text)
    ADC1_ResetSampling                       0x08005359   Thumb Code    32  main.o(.text)
    LoadSweepPhase2ToCorrection              0x08005379   Thumb Code   294  main.o(.text)
    ApplySmoothFilter                        0x0800549f   Thumb Code    90  main.o(.text)
    SaveSweepPhase2Data                      0x080054f9   Thumb Code   878  main.o(.text)
    ProcessSweepPoint                        0x08005867   Thumb Code  1302  main.o(.text)
    ADC3_StopSampling                        0x08005d7d   Thumb Code    12  main.o(.text)
    ADC3_IIRFilter_Process                   0x08005d89   Thumb Code    96  main.o(.text)
    InitializeIIRFilter                      0x08005de9   Thumb Code  2276  main.o(.text)
    CalculateFilterParameters                0x080066cd   Thumb Code  1668  main.o(.text)
    InitSmoothFilters                        0x08006d51   Thumb Code    58  main.o(.text)
    ClearSweepPhase2Data                     0x08006d8b   Thumb Code    44  main.o(.text)
    StartSweepTest                           0x08006db7   Thumb Code   254  main.o(.text)
    main                                     0x08006eb5   Thumb Code  7482  main.o(.text)
    GetAmplitudeResponseFromSweep            0x08008bef   Thumb Code   400  main.o(.text)
    ADC3_ReconstructSignal_Simplified        0x08008d7f   Thumb Code  1030  main.o(.text)
    ADC3_ReconstructSignal                   0x08009185   Thumb Code  1896  main.o(.text)
    ADC3_StartOptimizedDACOutput             0x080098ed   Thumb Code  1614  main.o(.text)
    ADC3_HarmonicFiltering                   0x08009f3b   Thumb Code  1750  main.o(.text)
    ADC3_GenerateCleanSineWave               0x0800a611   Thumb Code   710  main.o(.text)
    ADC3_ReconstructStandardSineWave         0x0800a8d7   Thumb Code  2130  main.o(.text)
    ADC3_AddSweepPoint                       0x0800b129   Thumb Code    76  main.o(.text)
    ADC3_LoadSweepResults                    0x0800b175   Thumb Code   664  main.o(.text)
    ADC3_EnableSweepCorrection               0x0800b40d   Thumb Code    26  main.o(.text)
    ADC3_ClearSweepResults                   0x0800b427   Thumb Code    40  main.o(.text)
    ADC3_PrintSweepResults                   0x0800b44f   Thumb Code   172  main.o(.text)
    ADC3_SetSweepData                        0x0800b4fb   Thumb Code   142  main.o(.text)
    NMI_Handler                              0x0800b68d   Thumb Code     2  stm32f4xx_it.o(.text)
    HardFault_Handler                        0x0800b68f   Thumb Code     4  stm32f4xx_it.o(.text)
    MemManage_Handler                        0x0800b693   Thumb Code     4  stm32f4xx_it.o(.text)
    BusFault_Handler                         0x0800b697   Thumb Code     4  stm32f4xx_it.o(.text)
    UsageFault_Handler                       0x0800b69b   Thumb Code     4  stm32f4xx_it.o(.text)
    SVC_Handler                              0x0800b69f   Thumb Code     2  stm32f4xx_it.o(.text)
    DebugMon_Handler                         0x0800b6a1   Thumb Code     2  stm32f4xx_it.o(.text)
    PendSV_Handler                           0x0800b6a3   Thumb Code     2  stm32f4xx_it.o(.text)
    SysTick_Handler                          0x0800b6a5   Thumb Code     2  stm32f4xx_it.o(.text)
    SystemInit                               0x0800b785   Thumb Code    88  system_stm32f4xx.o(.text)
    SystemCoreClockUpdate                    0x0800b7dd   Thumb Code   174  system_stm32f4xx.o(.text)
    Reset_Handler                            0x0800b8b9   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI0_IRQHandler                         0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM2_IRQHandler                          0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM6_DAC_IRQHandler                      0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART2_IRQHandler                        0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART3_IRQHandler                        0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x0800b8d3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __user_initial_stackheap                 0x0800b8d5   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    NVIC_PriorityGroupConfig                 0x0800b8f9   Thumb Code    10  misc.o(.text)
    NVIC_Init                                0x0800b903   Thumb Code   106  misc.o(.text)
    NVIC_SetVectorTable                      0x0800b96d   Thumb Code    14  misc.o(.text)
    NVIC_SystemLPConfig                      0x0800b97b   Thumb Code    34  misc.o(.text)
    SysTick_CLKSourceConfig                  0x0800b99d   Thumb Code    40  misc.o(.text)
    ADC_DeInit                               0x0800b9d9   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_Init                                 0x0800b9ef   Thumb Code    74  stm32f4xx_adc.o(.text)
    ADC_StructInit                           0x0800ba39   Thumb Code    20  stm32f4xx_adc.o(.text)
    ADC_CommonInit                           0x0800ba4d   Thumb Code    34  stm32f4xx_adc.o(.text)
    ADC_CommonStructInit                     0x0800ba6f   Thumb Code    12  stm32f4xx_adc.o(.text)
    ADC_Cmd                                  0x0800ba7b   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_AnalogWatchdogCmd                    0x0800ba91   Thumb Code    16  stm32f4xx_adc.o(.text)
    ADC_AnalogWatchdogThresholdsConfig       0x0800baa1   Thumb Code     6  stm32f4xx_adc.o(.text)
    ADC_AnalogWatchdogSingleChannelConfig    0x0800baa7   Thumb Code    16  stm32f4xx_adc.o(.text)
    ADC_TempSensorVrefintCmd                 0x0800bab7   Thumb Code    34  stm32f4xx_adc.o(.text)
    ADC_VBATCmd                              0x0800bad9   Thumb Code    34  stm32f4xx_adc.o(.text)
    ADC_RegularChannelConfig                 0x0800bafb   Thumb Code   184  stm32f4xx_adc.o(.text)
    ADC_SoftwareStartConv                    0x0800bbb3   Thumb Code    10  stm32f4xx_adc.o(.text)
    ADC_GetSoftwareStartConvStatus           0x0800bbbd   Thumb Code    20  stm32f4xx_adc.o(.text)
    ADC_EOCOnEachRegularChannelCmd           0x0800bbd1   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_ContinuousModeCmd                    0x0800bbe7   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_DiscModeChannelCountConfig           0x0800bbfd   Thumb Code    24  stm32f4xx_adc.o(.text)
    ADC_DiscModeCmd                          0x0800bc15   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_GetConversionValue                   0x0800bc2b   Thumb Code     8  stm32f4xx_adc.o(.text)
    ADC_GetMultiModeConversionValue          0x0800bc33   Thumb Code     8  stm32f4xx_adc.o(.text)
    ADC_DMACmd                               0x0800bc3b   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_DMARequestAfterLastTransferCmd       0x0800bc51   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_MultiModeDMARequestAfterLastTransferCmd 0x0800bc67   Thumb Code    34  stm32f4xx_adc.o(.text)
    ADC_InjectedChannelConfig                0x0800bc89   Thumb Code   130  stm32f4xx_adc.o(.text)
    ADC_InjectedSequencerLengthConfig        0x0800bd0b   Thumb Code    24  stm32f4xx_adc.o(.text)
    ADC_SetInjectedOffset                    0x0800bd23   Thumb Code    20  stm32f4xx_adc.o(.text)
    ADC_ExternalTrigInjectedConvConfig       0x0800bd37   Thumb Code    16  stm32f4xx_adc.o(.text)
    ADC_ExternalTrigInjectedConvEdgeConfig   0x0800bd47   Thumb Code    16  stm32f4xx_adc.o(.text)
    ADC_SoftwareStartInjectedConv            0x0800bd57   Thumb Code    10  stm32f4xx_adc.o(.text)
    ADC_GetSoftwareStartInjectedConvCmdStatus 0x0800bd61   Thumb Code    20  stm32f4xx_adc.o(.text)
    ADC_AutoInjectedConvCmd                  0x0800bd75   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_InjectedDiscModeCmd                  0x0800bd8b   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_GetInjectedConversionValue           0x0800bda1   Thumb Code    28  stm32f4xx_adc.o(.text)
    ADC_ITConfig                             0x0800bdbd   Thumb Code    56  stm32f4xx_adc.o(.text)
    ADC_GetFlagStatus                        0x0800bdf5   Thumb Code    18  stm32f4xx_adc.o(.text)
    ADC_ClearFlag                            0x0800be07   Thumb Code     6  stm32f4xx_adc.o(.text)
    ADC_GetITStatus                          0x0800be0d   Thumb Code    38  stm32f4xx_adc.o(.text)
    ADC_ClearITPendingBit                    0x0800be33   Thumb Code    10  stm32f4xx_adc.o(.text)
    DAC_DeInit                               0x0800be3d   Thumb Code    22  stm32f4xx_dac.o(.text)
    DAC_Init                                 0x0800be53   Thumb Code    46  stm32f4xx_dac.o(.text)
    DAC_StructInit                           0x0800be81   Thumb Code    12  stm32f4xx_dac.o(.text)
    DAC_Cmd                                  0x0800be8d   Thumb Code    34  stm32f4xx_dac.o(.text)
    DAC_SoftwareTriggerCmd                   0x0800beaf   Thumb Code    48  stm32f4xx_dac.o(.text)
    DAC_DualSoftwareTriggerCmd               0x0800bedf   Thumb Code    38  stm32f4xx_dac.o(.text)
    DAC_WaveGenerationCmd                    0x0800bf05   Thumb Code    36  stm32f4xx_dac.o(.text)
    DAC_SetChannel1Data                      0x0800bf29   Thumb Code    26  stm32f4xx_dac.o(.text)
    DAC_SetChannel2Data                      0x0800bf43   Thumb Code    26  stm32f4xx_dac.o(.text)
    DAC_SetDualChannelData                   0x0800bf5d   Thumb Code    32  stm32f4xx_dac.o(.text)
    DAC_GetDataOutputValue                   0x0800bf7d   Thumb Code    32  stm32f4xx_dac.o(.text)
    DAC_DMACmd                               0x0800bf9d   Thumb Code    38  stm32f4xx_dac.o(.text)
    DAC_ITConfig                             0x0800bfc3   Thumb Code    36  stm32f4xx_dac.o(.text)
    DAC_GetFlagStatus                        0x0800bfe7   Thumb Code    28  stm32f4xx_dac.o(.text)
    DAC_ClearFlag                            0x0800c003   Thumb Code    12  stm32f4xx_dac.o(.text)
    DAC_GetITStatus                          0x0800c00f   Thumb Code    44  stm32f4xx_dac.o(.text)
    DAC_ClearITPendingBit                    0x0800c03b   Thumb Code    12  stm32f4xx_dac.o(.text)
    DMA_DeInit                               0x0800c04d   Thumb Code   324  stm32f4xx_dma.o(.text)
    DMA_Init                                 0x0800c191   Thumb Code    82  stm32f4xx_dma.o(.text)
    DMA_StructInit                           0x0800c1e3   Thumb Code    34  stm32f4xx_dma.o(.text)
    DMA_Cmd                                  0x0800c205   Thumb Code    22  stm32f4xx_dma.o(.text)
    DMA_PeriphIncOffsetSizeConfig            0x0800c21b   Thumb Code    22  stm32f4xx_dma.o(.text)
    DMA_FlowControllerConfig                 0x0800c231   Thumb Code    22  stm32f4xx_dma.o(.text)
    DMA_SetCurrDataCounter                   0x0800c247   Thumb Code     4  stm32f4xx_dma.o(.text)
    DMA_GetCurrDataCounter                   0x0800c24b   Thumb Code     8  stm32f4xx_dma.o(.text)
    DMA_DoubleBufferModeConfig               0x0800c253   Thumb Code    24  stm32f4xx_dma.o(.text)
    DMA_DoubleBufferModeCmd                  0x0800c26b   Thumb Code    22  stm32f4xx_dma.o(.text)
    DMA_MemoryTargetConfig                   0x0800c281   Thumb Code    10  stm32f4xx_dma.o(.text)
    DMA_GetCurrentMemoryTarget               0x0800c28b   Thumb Code    20  stm32f4xx_dma.o(.text)
    DMA_GetCmdStatus                         0x0800c29f   Thumb Code    20  stm32f4xx_dma.o(.text)
    DMA_GetFIFOStatus                        0x0800c2b3   Thumb Code    12  stm32f4xx_dma.o(.text)
    DMA_GetFlagStatus                        0x0800c2bf   Thumb Code    56  stm32f4xx_dma.o(.text)
    DMA_ClearFlag                            0x0800c2f7   Thumb Code    40  stm32f4xx_dma.o(.text)
    DMA_ITConfig                             0x0800c31f   Thumb Code    58  stm32f4xx_dma.o(.text)
    DMA_GetITStatus                          0x0800c359   Thumb Code    84  stm32f4xx_dma.o(.text)
    DMA_ClearITPendingBit                    0x0800c3ad   Thumb Code    40  stm32f4xx_dma.o(.text)
    GPIO_DeInit                              0x0800c3f5   Thumb Code   268  stm32f4xx_gpio.o(.text)
    GPIO_Init                                0x0800c501   Thumb Code   144  stm32f4xx_gpio.o(.text)
    GPIO_StructInit                          0x0800c591   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_PinLockConfig                       0x0800c5a3   Thumb Code    34  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x0800c5c5   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputData                       0x0800c5d7   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x0800c5df   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputData                      0x0800c5f1   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_SetBits                             0x0800c5f9   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ResetBits                           0x0800c5fd   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_WriteBit                            0x0800c601   Thumb Code    10  stm32f4xx_gpio.o(.text)
    GPIO_Write                               0x0800c60b   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ToggleBits                          0x0800c60f   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_PinAFConfig                         0x0800c617   Thumb Code    70  stm32f4xx_gpio.o(.text)
    RCC_DeInit                               0x0800c689   Thumb Code    82  stm32f4xx_rcc.o(.text)
    RCC_HSEConfig                            0x0800c6db   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_GetFlagStatus                        0x0800c6e9   Thumb Code    60  stm32f4xx_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x0800c725   Thumb Code    56  stm32f4xx_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x0800c75d   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_HSICmd                               0x0800c771   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_LSEConfig                            0x0800c777   Thumb Code    46  stm32f4xx_rcc.o(.text)
    RCC_LSICmd                               0x0800c7a5   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLConfig                            0x0800c7ab   Thumb Code    32  stm32f4xx_rcc.o(.text)
    RCC_PLLCmd                               0x0800c7cb   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SConfig                         0x0800c7d1   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SCmd                            0x0800c7df   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLSAIConfig                         0x0800c7e5   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PLLSAICmd                            0x0800c7f9   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x0800c7ff   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_MCO1Config                           0x0800c805   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_MCO2Config                           0x0800c821   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SYSCLKConfig                         0x0800c83d   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x0800c851   Thumb Code    12  stm32f4xx_rcc.o(.text)
    RCC_HCLKConfig                           0x0800c85d   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PCLK1Config                          0x0800c871   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PCLK2Config                          0x0800c885   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_GetClocksFreq                        0x0800c89b   Thumb Code   222  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKConfig                         0x0800c979   Thumb Code    54  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKCmd                            0x0800c9af   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_BackupResetCmd                       0x0800c9b7   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_I2SCLKConfig                         0x0800c9bf   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLI2SClkDivConfig                0x0800c9c5   Thumb Code    26  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLSAIClkDivConfig                0x0800c9df   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockACLKConfig                   0x0800c9fb   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockBCLKConfig                   0x0800ca0f   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_LTDCCLKDivConfig                     0x0800ca23   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_TIMCLKPresConfig                     0x0800ca37   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockCmd                   0x0800ca3d   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockCmd                   0x0800ca5f   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockCmd                   0x0800caad   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x0800cacf   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x0800caf1   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphResetCmd                   0x0800cb13   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphResetCmd                   0x0800cb35   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphResetCmd                   0x0800cb57   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x0800cb79   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x0800cb9b   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockLPModeCmd             0x0800cbbd   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockLPModeCmd             0x0800cbdf   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockLPModeCmd             0x0800cc01   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockLPModeCmd             0x0800cc23   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockLPModeCmd             0x0800cc45   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_LSEModeConfig                        0x0800cc67   Thumb Code    40  stm32f4xx_rcc.o(.text)
    RCC_ITConfig                             0x0800cc8f   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_ClearFlag                            0x0800ccb1   Thumb Code    18  stm32f4xx_rcc.o(.text)
    RCC_GetITStatus                          0x0800ccc3   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_ClearITPendingBit                    0x0800ccd9   Thumb Code     8  stm32f4xx_rcc.o(.text)
    TIM_DeInit                               0x0800cce5   Thumb Code   346  stm32f4xx_tim.o(.text)
    TIM_TimeBaseInit                         0x0800ce3f   Thumb Code   104  stm32f4xx_tim.o(.text)
    TIM_TimeBaseStructInit                   0x0800cea7   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PrescalerConfig                      0x0800ceb9   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_CounterModeConfig                    0x0800cebf   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetCounter                           0x0800ced1   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetAutoreload                        0x0800ced5   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetCounter                           0x0800ced9   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetPrescaler                         0x0800cedf   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_UpdateDisableConfig                  0x0800cee5   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_UpdateRequestConfig                  0x0800cefd   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ARRPreloadConfig                     0x0800cf15   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_SelectOnePulseMode                   0x0800cf2d   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetClockDivision                     0x0800cf3f   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_Cmd                                  0x0800cf51   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_OC1Init                              0x0800cf69   Thumb Code   114  stm32f4xx_tim.o(.text)
    TIM_OC2Init                              0x0800cfdb   Thumb Code   154  stm32f4xx_tim.o(.text)
    TIM_OC3Init                              0x0800d075   Thumb Code   204  stm32f4xx_tim.o(.text)
    TIM_OC4Init                              0x0800d141   Thumb Code   112  stm32f4xx_tim.o(.text)
    TIM_OCStructInit                         0x0800d1b1   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_SelectOCxM                           0x0800d1c5   Thumb Code    86  stm32f4xx_tim.o(.text)
    TIM_SetCompare1                          0x0800d21b   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare2                          0x0800d21f   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare3                          0x0800d223   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare4                          0x0800d227   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_ForcedOC1Config                      0x0800d22b   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ForcedOC2Config                      0x0800d23d   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_ForcedOC3Config                      0x0800d257   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ForcedOC4Config                      0x0800d269   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC1PreloadConfig                     0x0800d283   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2PreloadConfig                     0x0800d295   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3PreloadConfig                     0x0800d2af   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC4PreloadConfig                     0x0800d2c1   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC1FastConfig                        0x0800d2db   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2FastConfig                        0x0800d2ed   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3FastConfig                        0x0800d307   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC4FastConfig                        0x0800d319   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_ClearOC1Ref                          0x0800d333   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearOC2Ref                          0x0800d345   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ClearOC3Ref                          0x0800d35d   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearOC4Ref                          0x0800d36f   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_OC1PolarityConfig                    0x0800d387   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC1NPolarityConfig                   0x0800d399   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2PolarityConfig                    0x0800d3ab   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC2NPolarityConfig                   0x0800d3c5   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3PolarityConfig                    0x0800d3df   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3NPolarityConfig                   0x0800d3f9   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC4PolarityConfig                    0x0800d413   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_CCxCmd                               0x0800d42d   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_CCxNCmd                              0x0800d44b   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_SetIC4Prescaler                      0x0800d469   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_SetIC3Prescaler                      0x0800d4d3   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetIC2Prescaler                      0x0800d52d   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_SetIC1Prescaler                      0x0800d5a1   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ICInit                               0x0800d5ed   Thumb Code   110  stm32f4xx_tim.o(.text)
    TIM_ICStructInit                         0x0800d65b   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PWMIConfig                           0x0800d66d   Thumb Code   124  stm32f4xx_tim.o(.text)
    TIM_GetCapture1                          0x0800d6e9   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture2                          0x0800d6ef   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture3                          0x0800d6f5   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture4                          0x0800d6fb   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_BDTRConfig                           0x0800d701   Thumb Code    32  stm32f4xx_tim.o(.text)
    TIM_BDTRStructInit                       0x0800d721   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_CtrlPWMOutputs                       0x0800d733   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_SelectCOM                            0x0800d751   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_CCPreloadControl                     0x0800d769   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ITConfig                             0x0800d781   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_GenerateEvent                        0x0800d793   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetFlagStatus                        0x0800d797   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearFlag                            0x0800d7a9   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetITStatus                          0x0800d7af   Thumb Code    34  stm32f4xx_tim.o(.text)
    TIM_ClearITPendingBit                    0x0800d7d1   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_DMAConfig                            0x0800d7d7   Thumb Code    10  stm32f4xx_tim.o(.text)
    TIM_DMACmd                               0x0800d7e1   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectCCDMA                          0x0800d7f3   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_InternalClockConfig                  0x0800d80b   Thumb Code    12  stm32f4xx_tim.o(.text)
    TIM_SelectInputTrigger                   0x0800d817   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ITRxExternalClockConfig              0x0800d829   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_TIxExternalClockConfig               0x0800d841   Thumb Code    62  stm32f4xx_tim.o(.text)
    TIM_ETRConfig                            0x0800d87f   Thumb Code    28  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode1Config                  0x0800d89b   Thumb Code    54  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode2Config                  0x0800d8d1   Thumb Code    32  stm32f4xx_tim.o(.text)
    TIM_SelectOutputTrigger                  0x0800d8f1   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectSlaveMode                      0x0800d903   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectMasterSlaveMode                0x0800d915   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_EncoderInterfaceConfig               0x0800d927   Thumb Code    66  stm32f4xx_tim.o(.text)
    TIM_SelectHallSensor                     0x0800d969   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_RemapConfig                          0x0800d981   Thumb Code     6  stm32f4xx_tim.o(.text)
    USART_DeInit                             0x0800d989   Thumb Code   206  stm32f4xx_usart.o(.text)
    USART_Init                               0x0800da57   Thumb Code   204  stm32f4xx_usart.o(.text)
    USART_StructInit                         0x0800db23   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_ClockInit                          0x0800db3b   Thumb Code    32  stm32f4xx_usart.o(.text)
    USART_ClockStructInit                    0x0800db5b   Thumb Code    12  stm32f4xx_usart.o(.text)
    USART_Cmd                                0x0800db67   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetPrescaler                       0x0800db7f   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_OverSampling8Cmd                   0x0800db8f   Thumb Code    22  stm32f4xx_usart.o(.text)
    USART_OneBitMethodCmd                    0x0800dba5   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendData                           0x0800dbbd   Thumb Code     8  stm32f4xx_usart.o(.text)
    USART_ReceiveData                        0x0800dbc5   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_SetAddress                         0x0800dbcf   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x0800dbe1   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_WakeUpConfig                       0x0800dbf9   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x0800dc0b   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_LINCmd                             0x0800dc1d   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendBreak                          0x0800dc35   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_HalfDuplexCmd                      0x0800dc3f   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetGuardTime                       0x0800dc57   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_SmartCardCmd                       0x0800dc67   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SmartCardNACKCmd                   0x0800dc7f   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_IrDAConfig                         0x0800dc97   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_IrDACmd                            0x0800dca9   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_DMACmd                             0x0800dcc1   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_ITConfig                           0x0800dcd3   Thumb Code    74  stm32f4xx_usart.o(.text)
    USART_GetFlagStatus                      0x0800dd1d   Thumb Code    26  stm32f4xx_usart.o(.text)
    USART_ClearFlag                          0x0800dd37   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_GetITStatus                        0x0800dd49   Thumb Code   118  stm32f4xx_usart.o(.text)
    USART_ClearITPendingBit                  0x0800ddbf   Thumb Code    30  stm32f4xx_usart.o(.text)
    delay_init                               0x0800dddd   Thumb Code    52  delay.o(.text)
    delay_us                                 0x0800de11   Thumb Code    72  delay.o(.text)
    delay_xms                                0x0800de59   Thumb Code    72  delay.o(.text)
    delay_ms                                 0x0800dea1   Thumb Code    56  delay.o(.text)
    sys_nvic_set_vector_table                0x0800dee1   Thumb Code    12  sys.o(.text)
    sys_nvic_init                            0x0800df11   Thumb Code   116  sys.o(.text)
    sys_nvic_ex_config                       0x0800df85   Thumb Code   272  sys.o(.text)
    sys_gpio_af_set                          0x0800e095   Thumb Code   104  sys.o(.text)
    sys_gpio_set                             0x0800e0fd   Thumb Code   222  sys.o(.text)
    sys_gpio_pin_set                         0x0800e1db   Thumb Code    14  sys.o(.text)
    sys_gpio_pin_get                         0x0800e1e9   Thumb Code    16  sys.o(.text)
    sys_wfi_set                              0x0800e1f9   Thumb Code     4  sys.o(.text)
    sys_intx_disable                         0x0800e1fd   Thumb Code     4  sys.o(.text)
    sys_intx_enable                          0x0800e201   Thumb Code     4  sys.o(.text)
    sys_msr_msp                              0x0800e205   Thumb Code    10  sys.o(.text)
    sys_standby                              0x0800e20f   Thumb Code    72  sys.o(.text)
    sys_soft_reset                           0x0800e257   Thumb Code    12  sys.o(.text)
    sys_clock_set                            0x0800e263   Thumb Code   434  sys.o(.text)
    sys_stm32_clock_init                     0x0800e415   Thumb Code    60  sys.o(.text)
    _sys_exit                                0x0800e459   Thumb Code     4  usart.o(.text)
    fputc                                    0x0800e45d   Thumb Code    22  usart.o(.text)
    uart_init                                0x0800e473   Thumb Code   164  usart.o(.text)
    USART1_IRQHandler                        0x0800e517   Thumb Code   138  usart.o(.text)
    key_config                               0x0800e5b5   Thumb Code   548  stm32f4_key.o(.text)
    LED_Init                                 0x0800e7e9   Thumb Code    60  led.o(.text)
    TIM3_Int_Init                            0x0800e829   Thumb Code    84  timer.o(.text)
    TIM4_Int_Init                            0x0800e87d   Thumb Code    88  timer.o(.text)
    TIM3_IRQHandler                          0x0800e8d5   Thumb Code    24  timer.o(.text)
    TIM4_IRQHandler                          0x0800e8ed   Thumb Code    60  timer.o(.text)
    TIM6_DAC_Init                            0x0800e929   Thumb Code    48  timer.o(.text)
    TIM5_ADC3_Init                           0x0800e959   Thumb Code    80  timer.o(.text)
    TIM5_IRQHandler                          0x0800e9a9   Thumb Code    78  timer.o(.text)
    QCZ_FFT                                  0x0800ea25   Thumb Code     2  adc.o(.text)
    QCZ_FFT1                                 0x0800ea27   Thumb Code     2  adc.o(.text)
    Adc_Init                                 0x0800ea29   Thumb Code   230  adc.o(.text)
    DMA1_Init                                0x0800eb0f   Thumb Code   180  adc.o(.text)
    DMA2_Stream0_IRQHandler                  0x0800ebc3   Thumb Code    36  adc.o(.text)
    Adc2_Init                                0x0800ebe7   Thumb Code   230  adc.o(.text)
    DMA2_Init                                0x0800eccd   Thumb Code   198  adc.o(.text)
    DMA2_Stream2_IRQHandler                  0x0800ed93   Thumb Code    40  adc.o(.text)
    Adc3_Init                                0x0800edbb   Thumb Code   242  adc.o(.text)
    DMA3_Init                                0x0800eead   Thumb Code   184  adc.o(.text)
    DMA2_Stream1_IRQHandler                  0x0800ef65   Thumb Code    36  adc.o(.text)
    ADC_IRQHandler                           0x0800ef89   Thumb Code   304  adc.o(.text)
    lcd_wr_data                              0x0800f12d   Thumb Code    18  lcd.o(.text)
    lcd_wr_regno                             0x0800f13f   Thumb Code    20  lcd.o(.text)
    lcd_write_reg                            0x0800f153   Thumb Code    16  lcd.o(.text)
    lcd_write_ram_prepare                    0x0800f183   Thumb Code    12  lcd.o(.text)
    lcd_set_cursor                           0x0800f18f   Thumb Code   282  lcd.o(.text)
    lcd_read_point                           0x0800f2a9   Thumb Code   148  lcd.o(.text)
    lcd_display_on                           0x0800f33d   Thumb Code    32  lcd.o(.text)
    lcd_display_off                          0x0800f35d   Thumb Code    32  lcd.o(.text)
    lcd_scan_dir                             0x0800f37d   Thumb Code   578  lcd.o(.text)
    lcd_draw_point                           0x0800f5bf   Thumb Code    26  lcd.o(.text)
    lcd_ssd_backlight_set                    0x0800f5d9   Thumb Code    88  lcd.o(.text)
    lcd_display_dir                          0x0800f631   Thumb Code   370  lcd.o(.text)
    lcd_set_window                           0x0800f7a3   Thumb Code   370  lcd.o(.text)
    lcd_clear                                0x0800f915   Thumb Code    64  lcd.o(.text)
    lcd_init                                 0x0800f955   Thumb Code  1470  lcd.o(.text)
    lcd_fill                                 0x0800ff13   Thumb Code    80  lcd.o(.text)
    lcd_color_fill                           0x0800ff63   Thumb Code    92  lcd.o(.text)
    lcd_draw_line                            0x0800ffbf   Thumb Code   172  lcd.o(.text)
    lcd_draw_hline                           0x0801006b   Thumb Code    52  lcd.o(.text)
    lcd_draw_rectangle                       0x0801009f   Thumb Code    74  lcd.o(.text)
    lcd_draw_circle                          0x080100e9   Thumb Code   188  lcd.o(.text)
    lcd_fill_circle                          0x080101a5   Thumb Code   176  lcd.o(.text)
    lcd_show_char                            0x08010255   Thumb Code   294  lcd.o(.text)
    lcd_show_num                             0x08010391   Thumb Code   152  lcd.o(.text)
    lcd_show_xnum                            0x08010429   Thumb Code   198  lcd.o(.text)
    lcd_show_string                          0x080104ef   Thumb Code   106  lcd.o(.text)
    lcd_ex_st7789_reginit                    0x08010571   Thumb Code   424  lcd_ex.o(.text)
    lcd_ex_ili9341_reginit                   0x08010719   Thumb Code   556  lcd_ex.o(.text)
    lcd_ex_nt35310_reginit                   0x08010945   Thumb Code  3826  lcd_ex.o(.text)
    lcd_ex_st7796_reginit                    0x08011837   Thumb Code   454  lcd_ex.o(.text)
    lcd_ex_nt35510_reginit                   0x080119fd   Thumb Code  3950  lcd_ex.o(.text)
    lcd_ex_ili9806_reginit                   0x0801296b   Thumb Code   832  lcd_ex.o(.text)
    lcd_ex_ssd1963_reginit                   0x08012cab   Thumb Code   366  lcd_ex.o(.text)
    AD983_GPIO_Init                          0x08012e19   Thumb Code    42  ad9833.o(.text)
    AD9833_SPI_Write                         0x08012e43   Thumb Code   186  ad9833.o(.text)
    AD9833_SetRegisterValue                  0x08012efd   Thumb Code    38  ad9833.o(.text)
    AD9833_Init                              0x08012f23   Thumb Code    18  ad9833.o(.text)
    AD9833_Reset                             0x08012f35   Thumb Code    20  ad9833.o(.text)
    AD9833_ClearReset                        0x08012f49   Thumb Code    12  ad9833.o(.text)
    AD9833_SetFrequency                      0x08012f55   Thumb Code   116  ad9833.o(.text)
    AD9833_SetFrequencyQuick                 0x08012fc9   Thumb Code    32  ad9833.o(.text)
    AD9833_SetPhase                          0x08012fe9   Thumb Code    20  ad9833.o(.text)
    AD9833_Setup                             0x08012ffd   Thumb Code    32  ad9833.o(.text)
    AD9833_SetWave                           0x0801301d   Thumb Code    14  ad9833.o(.text)
    AD983_GPIO_Init1                         0x0801302b   Thumb Code    42  ad9833.o(.text)
    AD9833_SetRegisterValue1                 0x08013055   Thumb Code    14  ad9833.o(.text)
    AD9833_Init1                             0x08013063   Thumb Code    16  ad9833.o(.text)
    AD9833_Reset1                            0x08013073   Thumb Code    18  ad9833.o(.text)
    AD9833_ClearReset1                       0x08013085   Thumb Code    10  ad9833.o(.text)
    AD9833_SetFrequency1                     0x0801308f   Thumb Code   110  ad9833.o(.text)
    AD9833_SetFrequencyQuick1                0x080130fd   Thumb Code    32  ad9833.o(.text)
    AD9833_SetPhase1                         0x0801311d   Thumb Code    18  ad9833.o(.text)
    AD9833_Setup1                            0x0801312f   Thumb Code    30  ad9833.o(.text)
    AD9833_SetWave1                          0x0801314d   Thumb Code    12  ad9833.o(.text)
    DAC_SetChannel1Value                     0x08013175   Thumb Code    24  dac.o(.text)
    DAC_SetChannel1Voltage                   0x0801318d   Thumb Code    88  dac.o(.text)
    DAC_PA4_Init                             0x080131e5   Thumb Code    82  dac.o(.text)
    DAC_DMA_Init                             0x08013237   Thumb Code   144  dac.o(.text)
    DAC_GetAmplitudeForFrequency             0x08013439   Thumb Code   424  dac.o(.text)
    DAC_SineWave_Init                        0x080135e1   Thumb Code    36  dac.o(.text)
    DAC_StopSineOutput                       0x08013605   Thumb Code    34  dac.o(.text)
    DAC_StartSineOutput                      0x08013627   Thumb Code    70  dac.o(.text)
    DAC_SetSineFrequency                     0x08013759   Thumb Code   150  dac.o(.text)
    DAC_SetAmplitudeMultiplier               0x080137ef   Thumb Code    32  dac.o(.text)
    DAC_NextAmplitudeMultiplier              0x0801380f   Thumb Code    58  dac.o(.text)
    DAC_GetAmplitudeMultiplier               0x08013849   Thumb Code     8  dac.o(.text)
    DAC_SetUserEnable                        0x08013851   Thumb Code    28  dac.o(.text)
    DAC_GetUserEnable                        0x0801386d   Thumb Code     6  dac.o(.text)
    DAC_Enable                               0x08013873   Thumb Code    12  dac.o(.text)
    DAC_Disable                              0x0801387f   Thumb Code    12  dac.o(.text)
    DAC_Test                                 0x0801388b   Thumb Code    92  dac.o(.text)
    DMA1_Stream5_IRQHandler                  0x080138e7   Thumb Code    24  dac.o(.text)
    DAC_StartReconstructedOutput             0x080138ff   Thumb Code   526  dac.o(.text)
    DAC_StartOptimizedReconstructedOutput    0x08013b0d   Thumb Code   300  dac.o(.text)
    DAC_StopReconstructedOutput              0x08013c39   Thumb Code    44  dac.o(.text)
    DAC_GenerateMultiFrequencySignal         0x08013c65   Thumb Code  1242  dac.o(.text)
    DAC_GenerateSingleFrequencySignal        0x0801413f   Thumb Code   338  dac.o(.text)
    DAC_StopHarmonicOutput                   0x08014291   Thumb Code     8  dac.o(.text)
    DAC_SimpleTest                           0x08014299   Thumb Code    12  dac.o(.text)
    arm_cmplx_mag_f32                        0x080143d1   Thumb Code   236  arm_cmplx_mag_f32.o(.text)
    arm_cfft_radix8by2_f32                   0x080144c1   Thumb Code   388  arm_cfft_f32.o(.text)
    arm_cfft_radix8by4_f32                   0x08014645   Thumb Code  1042  arm_cfft_f32.o(.text)
    arm_cfft_f32                             0x08014a57   Thumb Code   320  arm_cfft_f32.o(.text)
    arm_cfft_radix4_init_f32                 0x08014b99   Thumb Code   146  arm_cfft_radix4_init_f32.o(.text)
    arm_radix8_butterfly_f32                 0x08014c59   Thumb Code  1244  arm_cfft_radix8_f32.o(.text)
    __use_no_semihosting                     0x08015135   Thumb Code     2  use_no_semi_2.o(.text)
    __2printf                                0x08015139   Thumb Code    20  noretval__2printf.o(.text)
    __2sprintf                               0x08015151   Thumb Code    34  noretval__2sprintf.o(.text)
    _printf_str                              0x08015179   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x080151cd   Thumb Code   104  _printf_dec.o(.text)
    __printf                                 0x08015245   Thumb Code   270  __printf_wp.o(.text)
    strlen                                   0x08015353   Thumb Code    62  strlen.o(.text)
    __aeabi_memcpy4                          0x08015391   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08015391   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08015391   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x080153d9   Thumb Code     0  rt_memcpy_w.o(.text)
    strcmp                                   0x080153f5   Thumb Code   128  strcmpv7m.o(.text)
    __use_two_region_memory                  0x08015475   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08015477   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08015479   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x0801547b   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x0801547b   Thumb Code     2  use_no_semi.o(.text)
    __read_errno                             0x0801547d   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x08015487   Thumb Code    12  _rserrno.o(.text)
    _printf_int_common                       0x08015493   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x08015545   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x080156f7   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x0801596f   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08015995   Thumb Code    10  _sputc.o(.text)
    _printf_cs_common                        0x0801599f   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x080159b3   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x080159c3   Thumb Code     8  _printf_char.o(.text)
    _printf_char_file                        0x080159cd   Thumb Code    32  _printf_char_file.o(.text)
    __rt_locale                              0x080159f1   Thumb Code     8  rt_locale_intlibspace.o(.text)
    __aeabi_errno_addr                       0x080159f9   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x080159f9   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x080159f9   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _ll_udiv10                               0x08015a01   Thumb Code   138  lludiv10.o(.text)
    _printf_fp_infnan                        0x08015a8d   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x08015b0d   Thumb Code   224  bigflt0.o(.text)
    ferror                                   0x08015bf1   Thumb Code     8  ferror.o(.text)
    __user_libspace                          0x08015bf9   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08015bf9   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08015bf9   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08015c01   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08015c4b   Thumb Code    18  exit.o(.text)
    _btod_d2e                                0x08015c5d   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08015c9b   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08015ce1   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08015d41   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08016079   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08016155   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x0801617f   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x080161a9   Thumb Code   580  btod.o(CL$$btod_mult_common)
    __ARM_fpclassify                         0x080163ed   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __ARM_fpclassifyf                        0x0801641d   Thumb Code    38  fpclassifyf.o(i.__ARM_fpclassifyf)
    __hardfp_atan                            0x08016449   Thumb Code   622  atan.o(i.__hardfp_atan)
    __hardfp_atan2                           0x08016721   Thumb Code   448  atan2.o(i.__hardfp_atan2)
    __hardfp_cosf                            0x08016921   Thumb Code   280  cosf.o(i.__hardfp_cosf)
    __hardfp_exp                             0x08016a71   Thumb Code   714  exp.o(i.__hardfp_exp)
    __hardfp_fabs                            0x08016dc9   Thumb Code    20  fabs.o(i.__hardfp_fabs)
    __hardfp_log                             0x08016de1   Thumb Code   872  log.o(i.__hardfp_log)
    __hardfp_log10f                          0x080171a5   Thumb Code   332  log10f.o(i.__hardfp_log10f)
    __hardfp_sinf                            0x08017325   Thumb Code   344  sinf.o(i.__hardfp_sinf)
    __hardfp_sinh                            0x080174b9   Thumb Code   426  sinh.o(i.__hardfp_sinh)
    __hardfp_sqrt                            0x080176a9   Thumb Code   122  sqrt.o(i.__hardfp_sqrt)
    __hardfp_sqrtf                           0x08017723   Thumb Code    58  sqrtf.o(i.__hardfp_sqrtf)
    __kernel_poly                            0x0801775d   Thumb Code   248  poly.o(i.__kernel_poly)
    __mathlib_dbl_divzero                    0x08017859   Thumb Code    28  dunder.o(i.__mathlib_dbl_divzero)
    __mathlib_dbl_infnan                     0x08017889   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_infnan2                    0x0801789d   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x080178b1   Thumb Code    24  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_overflow                   0x080178d1   Thumb Code    24  dunder.o(i.__mathlib_dbl_overflow)
    __mathlib_dbl_underflow                  0x080178f1   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    __mathlib_expm1                          0x08017911   Thumb Code  1268  expm1_i.o(i.__mathlib_expm1)
    __mathlib_flt_divzero                    0x08017e05   Thumb Code    14  funder.o(i.__mathlib_flt_divzero)
    __mathlib_flt_infnan                     0x08017e19   Thumb Code     6  funder.o(i.__mathlib_flt_infnan)
    __mathlib_flt_invalid                    0x08017e21   Thumb Code    10  funder.o(i.__mathlib_flt_invalid)
    __mathlib_flt_underflow                  0x08017e31   Thumb Code    10  funder.o(i.__mathlib_flt_underflow)
    __mathlib_rredf2                         0x08017e41   Thumb Code   316  rredf.o(i.__mathlib_rredf2)
    _is_digit                                0x08017f95   Thumb Code    14  __printf_wp.o(i._is_digit)
    atan                                     0x08017fa3   Thumb Code    16  atan.o(i.atan)
    exp                                      0x08017fb3   Thumb Code    16  exp.o(i.exp)
    fabs                                     0x08017fc3   Thumb Code    24  fabs.o(i.fabs)
    _get_lc_numeric                          0x08017fdd   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __aeabi_dneg                             0x08018009   Thumb Code     0  basic.o(x$fpl$basic)
    _dneg                                    0x08018009   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_fneg                             0x0801800f   Thumb Code     0  basic.o(x$fpl$basic)
    _fneg                                    0x0801800f   Thumb Code     6  basic.o(x$fpl$basic)
    _dabs                                    0x08018015   Thumb Code     6  basic.o(x$fpl$basic)
    _fabs                                    0x0801801b   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_d2f                              0x08018021   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08018021   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x08018085   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x08018085   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcmp_Inf                           0x080181d5   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_ddiv                             0x080181ed   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x080181ed   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_cdcmpeq                          0x0801849d   Thumb Code     0  deqf.o(x$fpl$deqf)
    _dcmpeq                                  0x0801849d   Thumb Code   120  deqf.o(x$fpl$deqf)
    __aeabi_d2iz                             0x08018515   Thumb Code     0  dfix.o(x$fpl$dfix)
    _dfix                                    0x08018515   Thumb Code    94  dfix.o(x$fpl$dfix)
    __aeabi_d2uiz                            0x08018575   Thumb Code     0  dfixu.o(x$fpl$dfixu)
    _dfixu                                   0x08018575   Thumb Code    90  dfixu.o(x$fpl$dfixu)
    __aeabi_i2d                              0x080185cf   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x080185cf   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_ui2d                             0x080185fd   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x080185fd   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_cdcmple                          0x08018625   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x08018625   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x08018687   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x0801869d   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x0801869d   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x080187f1   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x0801888d   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x08018899   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x08018899   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    __aeabi_drsub                            0x08018905   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x08018905   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    _dsqrt                                   0x0801891d   Thumb Code   404  dsqrt_umaal.o(x$fpl$dsqrt)
    __aeabi_dsub                             0x08018ab5   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x08018ab5   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x08018c89   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08018c89   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x08018cdf   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x08018d6b   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08018d73   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08018d73   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fpl_fretinf                            0x08018d75   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    _printf_fp_dec                           0x08018d7f   Thumb Code     4  printf1.o(x$fpl$printf1)
    __I$use$fp                               0x08018d82   Number         0  usenofp.o(x$fpl$usenofp)
    arm_cfft_sR_f32_len16                    0x08018d84   Data          16  main.o(.constdata)
    arm_cfft_sR_f32_len32                    0x08018d94   Data          16  main.o(.constdata)
    arm_cfft_sR_f32_len64                    0x08018da4   Data          16  main.o(.constdata)
    arm_cfft_sR_f32_len128                   0x08018db4   Data          16  main.o(.constdata)
    arm_cfft_sR_f32_len256                   0x08018dc4   Data          16  main.o(.constdata)
    arm_cfft_sR_f32_len512                   0x08018dd4   Data          16  main.o(.constdata)
    arm_cfft_sR_f32_len1024                  0x08018de4   Data          16  main.o(.constdata)
    arm_cfft_sR_f32_len2048                  0x08018df4   Data          16  main.o(.constdata)
    arm_cfft_sR_f32_len4096                  0x08018e04   Data          16  main.o(.constdata)
    asc2_1206                                0x08018e46   Data        1140  lcd.o(.constdata)
    asc2_1608                                0x080192ba   Data        1520  lcd.o(.constdata)
    asc2_2412                                0x080198aa   Data        3420  lcd.o(.constdata)
    asc2_3216                                0x0801a606   Data        6080  lcd.o(.constdata)
    armBitRevTable                           0x0801be48   Data        2048  arm_common_tables.o(.constdata)
    twiddleCoef_16                           0x0801c648   Data         128  arm_common_tables.o(.constdata)
    twiddleCoef_32                           0x0801c6c8   Data         256  arm_common_tables.o(.constdata)
    twiddleCoef_64                           0x0801c7c8   Data         512  arm_common_tables.o(.constdata)
    twiddleCoef_128                          0x0801c9c8   Data        1024  arm_common_tables.o(.constdata)
    twiddleCoef_256                          0x0801cdc8   Data        2048  arm_common_tables.o(.constdata)
    twiddleCoef_512                          0x0801d5c8   Data        4096  arm_common_tables.o(.constdata)
    twiddleCoef_1024                         0x0801e5c8   Data        8192  arm_common_tables.o(.constdata)
    twiddleCoef_2048                         0x080205c8   Data       16384  arm_common_tables.o(.constdata)
    twiddleCoef_4096                         0x080245c8   Data       32768  arm_common_tables.o(.constdata)
    armBitRevIndexTable16                    0x0802c5c8   Data          40  arm_common_tables.o(.constdata)
    armBitRevIndexTable32                    0x0802c5f0   Data          96  arm_common_tables.o(.constdata)
    armBitRevIndexTable64                    0x0802c650   Data         112  arm_common_tables.o(.constdata)
    armBitRevIndexTable128                   0x0802c6c0   Data         416  arm_common_tables.o(.constdata)
    armBitRevIndexTable256                   0x0802c860   Data         880  arm_common_tables.o(.constdata)
    armBitRevIndexTable512                   0x0802cbd0   Data         896  arm_common_tables.o(.constdata)
    armBitRevIndexTable1024                  0x0802cf50   Data        3600  arm_common_tables.o(.constdata)
    armBitRevIndexTable2048                  0x0802dd60   Data        7616  arm_common_tables.o(.constdata)
    armBitRevIndexTable4096                  0x0802fb20   Data        8064  arm_common_tables.o(.constdata)
    __mathlib_zero                           0x08031c38   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x08032a94   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08032ab4   Number         0  anon$$obj.o(Region$$Table)
    Separate                                 0x20000000   Data           1  main.o(.data)
    frequency_A                              0x20000004   Data           4  main.o(.data)
    frequency_B                              0x20000008   Data           4  main.o(.data)
    phase_difference_A                       0x2000000c   Data           4  main.o(.data)
    phase_difference_B                       0x20000010   Data           4  main.o(.data)
    phase_difference_A1                      0x20000014   Data           4  main.o(.data)
    phase_difference_B1                      0x20000018   Data           4  main.o(.data)
    current_output_freq_A                    0x20000020   Data           8  main.o(.data)
    current_output_freq_B                    0x20000028   Data           8  main.o(.data)
    phase_A_CS                               0x20000030   Data           4  main.o(.data)
    phase_B_CS                               0x20000034   Data           4  main.o(.data)
    phase_A_SX                               0x20000038   Data           4  main.o(.data)
    phase_B_SX                               0x2000003c   Data           4  main.o(.data)
    current_phase_B                          0x20000040   Data           2  main.o(.data)
    peak_idx                                 0x20000044   Data           4  main.o(.data)
    QCZ                                      0x20000048   Data           1  main.o(.data)
    QCZ1                                     0x20000049   Data           1  main.o(.data)
    QCZ_Phase                                0x2000004c   Data           8  main.o(.data)
    QCZ_Phase1                               0x20000054   Data           8  main.o(.data)
    Phase                                    0x2000005c   Data           4  main.o(.data)
    ZE                                       0x20000060   Data           4  main.o(.data)
    SBP                                      0x20000064   Data           4  main.o(.data)
    waveform_A                               0x20000068   Data           2  main.o(.data)
    waveform_B                               0x2000006a   Data           2  main.o(.data)
    waveform_A_prime                         0x2000006c   Data           2  main.o(.data)
    waveform_B_prime                         0x2000006e   Data           2  main.o(.data)
    current_frequency                        0x20000070   Data           4  main.o(.data)
    key0_pressed                             0x20000074   Data           1  main.o(.data)
    key1_pressed                             0x20000075   Data           1  main.o(.data)
    key2_pressed                             0x20000076   Data           1  main.o(.data)
    wk_pressed                               0x20000077   Data           1  main.o(.data)
    frequency_changed                        0x20000078   Data           1  main.o(.data)
    dac_multiplier_changed                   0x20000079   Data           1  main.o(.data)
    dac_enable_changed                       0x2000007a   Data           1  main.o(.data)
    adc_enable_changed                       0x2000007b   Data           1  main.o(.data)
    adc_user_enabled                         0x2000007c   Data           1  main.o(.data)
    selected_button                          0x2000007d   Data           1  main.o(.data)
    adc1_sample_index                        0x2000007e   Data           2  main.o(.data)
    adc1_sampling_complete                   0x20000080   Data           1  main.o(.data)
    adc2_sample_index                        0x20000082   Data           2  main.o(.data)
    adc2_sampling_complete                   0x20000084   Data           1  main.o(.data)
    adc3_sample_index                        0x20000086   Data           2  main.o(.data)
    adc3_sampling_complete                   0x20000088   Data           1  main.o(.data)
    adc3_user_enabled                        0x20000089   Data           1  main.o(.data)
    adc3_fundamental_freq                    0x2000008c   Data           4  main.o(.data)
    adc3_points_per_cycle                    0x20000090   Data           2  main.o(.data)
    adc3_dac_sample_rate                     0x20000094   Data           4  main.o(.data)
    sweep_correction_count                   0x20000098   Data           4  main.o(.data)
    use_sweep_correction                     0x2000009c   Data           1  main.o(.data)
    sweep_phase2_count                       0x200000a0   Data           4  main.o(.data)
    sweep_phase2_completed                   0x200000a4   Data           1  main.o(.data)
    filter_params_calculated                 0x200000a5   Data           1  main.o(.data)
    iir_filter_enabled                       0x200000a6   Data           1  main.o(.data)
    adc3_timeout_counter                     0x200000a8   Data           4  main.o(.data)
    sweep_test_active                        0x200000ac   Data           1  main.o(.data)
    current_sweep_point                      0x200000ae   Data           2  main.o(.data)
    sweep_sampling_complete                  0x200000b0   Data           1  main.o(.data)
    total_sweep_points                       0x200000b2   Data           2  main.o(.data)
    max_voltage_ratio                        0x200000b4   Data           4  main.o(.data)
    sweep_phase                              0x200000b8   Data           1  main.o(.data)
    smooth_index_phase2                      0x200000b9   Data           1  main.o(.data)
    smooth_index_phase3                      0x200000ba   Data           1  main.o(.data)
    smooth_count_phase2                      0x200000bb   Data           1  main.o(.data)
    smooth_count_phase3                      0x200000bc   Data           1  main.o(.data)
    freq_1kHz_ratio                          0x200000c0   Data           4  main.o(.data)
    freq_1_2kHz_ratio                        0x200000c4   Data           4  main.o(.data)
    freq_399_8kHz_ratio                      0x200000c8   Data           4  main.o(.data)
    freq_400kHz_ratio                        0x200000cc   Data           4  main.o(.data)
    first_sweep_1kHz_ratio                   0x200000d0   Data           4  main.o(.data)
    first_sweep_1_2kHz_ratio                 0x200000d4   Data           4  main.o(.data)
    amplitude_multiplier                     0x200000d8   Data           1  main.o(.data)
    low_freq_points_completed                0x200000d9   Data           1  main.o(.data)
    buttons                                  0x200000dc   Data         160  main.o(.data)
    SystemCoreClock                          0x20000180   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x20000184   Data          16  system_stm32f4xx.o(.data)
    Res                                      0x200001a8   Data           1  usart.o(.data)
    __stdout                                 0x200001ac   Data           4  usart.o(.data)
    USART_RX_STA                             0x200001b0   Data           2  usart.o(.data)
    KAISHI                                   0x200001b4   Data           4  timer.o(.data)
    Adresult1                                0x200001b8   Data           4  adc.o(.data)
    Adresult2                                0x200001bc   Data           4  adc.o(.data)
    Adresult3                                0x200001c0   Data           4  adc.o(.data)
    frequency1                               0x200001c4   Data           4  adc.o(.data)
    frequency2                               0x200001c8   Data           4  adc.o(.data)
    frequency3                               0x200001cc   Data           4  adc.o(.data)
    phase                                    0x200001d0   Data           4  adc.o(.data)
    phase_A                                  0x200001d4   Data           4  adc.o(.data)
    phase_B                                  0x200001d8   Data           4  adc.o(.data)
    flag_ADC                                 0x200001dc   Data           1  adc.o(.data)
    flag_ADC1                                0x200001dd   Data           1  adc.o(.data)
    flag_ADC2                                0x200001de   Data           1  adc.o(.data)
    sampfre                                  0x200001e0   Data           4  fft.o(.data)
    flag3                                    0x200001e4   Data           1  fft.o(.data)
    Adresult                                 0x200001e8   Data           4  fft.o(.data)
    thd                                      0x200001ec   Data           4  fft.o(.data)
    k                                        0x200001f0   Data           4  fft.o(.data)
    frequency                                0x200001f4   Data           4  fft.o(.data)
    u                                        0x200001f8   Data           1  fft.o(.data)
    elec                                     0x200001fc   Data           4  fft.o(.data)
    set_right                                0x20000200   Data           4  fft.o(.data)
    set_rightk                               0x20000204   Data           4  fft.o(.data)
    len                                      0x20000208   Data           1  fft.o(.data)
    peak1_idx                                0x2000020c   Data           4  fft.o(.data)
    peak2_idx                                0x20000210   Data           4  fft.o(.data)
    length                                   0x20000214   Data           4  fft.o(.data)
    timef                                    0x20000218   Data           2  fft.o(.data)
    g_point_color                            0x2000021c   Data           4  lcd.o(.data)
    g_back_color                             0x20000220   Data           4  lcd.o(.data)
    dac_output_frequency                     0x20000228   Data           4  dac.o(.data)
    dac_output_enabled                       0x2000022c   Data           1  dac.o(.data)
    dac_user_enabled                         0x2000022d   Data           1  dac.o(.data)
    dac_amplitude_multiplier                 0x20000230   Data           4  dac.o(.data)
    lcd_buffer                               0x2000023c   Data          50  main.o(.bss)
    adc1_sample_buffer                       0x2000026e   Data        1024  main.o(.bss)
    adc2_sample_buffer                       0x2000066e   Data        1024  main.o(.bss)
    adc3_sample_buffer                       0x20000a6e   Data        1024  main.o(.bss)
    adc3_fft_inputbuf                        0x20000e70   Data        4096  main.o(.bss)
    adc3_fft_outputbuf                       0x20001e70   Data        2048  main.o(.bss)
    adc3_dc_removed                          0x20002670   Data        2048  main.o(.bss)
    adc3_ifft_inputbuf                       0x20002e70   Data        4096  main.o(.bss)
    adc3_ifft_512_buf                        0x20003e70   Data        4096  main.o(.bss)
    adc3_reconstructed                       0x20004e70   Data        1024  main.o(.bss)
    adc3_cycle_buffer                        0x20005270   Data        1024  main.o(.bss)
    sweep_correction_data                    0x20005670   Data         600  main.o(.bss)
    sweep_phase2_data                        0x200058c8   Data         360  main.o(.bss)
    detected_filter_params                   0x20005a30   Data          32  main.o(.bss)
    iir_filter                               0x20005a50   Data          36  main.o(.bss)
    sweep_results                            0x20005a74   Data         320  main.o(.bss)
    smooth_buffer_phase2                     0x20005bb4   Data          12  main.o(.bss)
    smooth_buffer_phase3                     0x20005bc0   Data          12  main.o(.bss)
    USART_RX_BUF                             0x20005bcc   Data         200  usart.o(.bss)
    buff_adc                                 0x20005c94   Data        8192  adc.o(.bss)
    buff_adc2                                0x20007c94   Data        8192  adc.o(.bss)
    buff_adc3                                0x20009c94   Data        8192  adc.o(.bss)
    scfft                                    0x2000bc94   Data          20  fft.o(.bss)
    fft_inputbuf                             0x2000bca8   Data       32768  fft.o(.bss)
    fft_outputbuf                            0x20013ca8   Data       16384  fft.o(.bss)
    Vpp_buff                                 0x20017ca8   Data          20  fft.o(.bss)
    fre                                      0x20017cbc   Data          20  fft.o(.bss)
    ele                                      0x20017cd0   Data          20  fft.o(.bss)
    all_vpp_fre                              0x20017ce4   Data          80  fft.o(.bss)
    effective_value                          0x20017d34   Data          24  fft.o(.bss)
    n                                        0x20017d4c   Data        8172  fft.o(.bss)
    m                                        0x20019d38   Data        8172  fft.o(.bss)
    lcddev                                   0x2001bd24   Data          14  lcd.o(.bss)
    __libspace_start                         0x2001bf34   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x2001bf94   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00032d0c, Max: 0x00100000, ABSOLUTE, COMPRESSED[0x00032b60])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00032ad0, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO          264    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000008   Code   RO         1705  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         2200    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000005a   Code   RO         2198    !!dczerorl2         c_w.l(__dczerorl2.o)
    0x0800021e   0x0800021e   0x00000002   PAD
    0x08000220   0x08000220   0x0000001c   Code   RO         2202    !!handler_zi        c_w.l(__scatter_zi.o)
    0x0800023c   0x0800023c   0x0000006a   Code   RO         1631    . text              arm_cortexM4lf_math.lib(arm_bitreversal2.o)
    0x080002a6   0x080002a6   0x00000000   Code   RO         1696    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080002a6   0x080002a6   0x00000006   Code   RO         1695    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x080002ac   0x080002ac   0x00000006   Code   RO         1694    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x080002b2   0x080002b2   0x00000006   Code   RO         1693    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x080002b8   0x080002b8   0x00000004   Code   RO         1888    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080002bc   0x080002bc   0x00000002   Code   RO         2073    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080002be   0x080002be   0x00000004   Code   RO         2074    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x080002c2   0x080002c2   0x00000000   Code   RO         2077    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080002c2   0x080002c2   0x00000000   Code   RO         2080    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080002c2   0x080002c2   0x00000000   Code   RO         2082    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080002c2   0x080002c2   0x00000000   Code   RO         2084    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080002c2   0x080002c2   0x00000006   Code   RO         2085    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x080002c8   0x080002c8   0x00000000   Code   RO         2087    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080002c8   0x080002c8   0x00000000   Code   RO         2089    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080002c8   0x080002c8   0x00000000   Code   RO         2091    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080002c8   0x080002c8   0x0000000a   Code   RO         2092    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         2093    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         2095    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         2097    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         2099    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         2101    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         2103    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         2105    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         2107    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         2111    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         2113    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         2115    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         2117    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000002   Code   RO         2118    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080002d4   0x080002d4   0x00000002   Code   RO         2146    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080002d6   0x080002d6   0x00000000   Code   RO         2155    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080002d6   0x080002d6   0x00000000   Code   RO         2157    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080002d6   0x080002d6   0x00000000   Code   RO         2159    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080002d6   0x080002d6   0x00000000   Code   RO         2162    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080002d6   0x080002d6   0x00000000   Code   RO         2165    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080002d6   0x080002d6   0x00000000   Code   RO         2167    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080002d6   0x080002d6   0x00000000   Code   RO         2170    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x080002d6   0x080002d6   0x00000002   Code   RO         2171    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x080002d8   0x080002d8   0x00000000   Code   RO         1873    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080002d8   0x080002d8   0x00000000   Code   RO         1993    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080002d8   0x080002d8   0x00000006   Code   RO         2005    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080002de   0x080002de   0x00000000   Code   RO         1995    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080002de   0x080002de   0x00000004   Code   RO         1996    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080002e2   0x080002e2   0x00000000   Code   RO         1998    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080002e2   0x080002e2   0x00000008   Code   RO         1999    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080002ea   0x080002ea   0x00000002   Code   RO         2119    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080002ec   0x080002ec   0x00000000   Code   RO         2126    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080002ec   0x080002ec   0x00000004   Code   RO         2127    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080002f0   0x080002f0   0x00000006   Code   RO         2128    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080002f6   0x080002f6   0x00000002   PAD
    0x080002f8   0x080002f8   0x0000b394   Code   RO            3    .text               main.o
    0x0800b68c   0x0800b68c   0x0000001a   Code   RO          215    .text               stm32f4xx_it.o
    0x0800b6a6   0x0800b6a6   0x00000002   PAD
    0x0800b6a8   0x0800b6a8   0x00000210   Code   RO          238    .text               system_stm32f4xx.o
    0x0800b8b8   0x0800b8b8   0x00000040   Code   RO          265    .text               startup_stm32f40_41xxx.o
    0x0800b8f8   0x0800b8f8   0x000000e0   Code   RO          271    .text               misc.o
    0x0800b9d8   0x0800b9d8   0x00000464   Code   RO          317    .text               stm32f4xx_adc.o
    0x0800be3c   0x0800be3c   0x00000210   Code   RO          457    .text               stm32f4xx_dac.o
    0x0800c04c   0x0800c04c   0x000003a8   Code   RO          540    .text               stm32f4xx_dma.o
    0x0800c3f4   0x0800c3f4   0x00000294   Code   RO          646    .text               stm32f4xx_gpio.o
    0x0800c688   0x0800c688   0x0000065c   Code   RO          809    .text               stm32f4xx_rcc.o
    0x0800cce4   0x0800cce4   0x00000ca2   Code   RO          954    .text               stm32f4xx_tim.o
    0x0800d986   0x0800d986   0x00000002   PAD
    0x0800d988   0x0800d988   0x00000454   Code   RO          974    .text               stm32f4xx_usart.o
    0x0800dddc   0x0800dddc   0x00000104   Code   RO         1014    .text               delay.o
    0x0800dee0   0x0800dee0   0x00000578   Code   RO         1037    .text               sys.o
    0x0800e458   0x0800e458   0x0000015c   Code   RO         1063    .text               usart.o
    0x0800e5b4   0x0800e5b4   0x00000234   Code   RO         1094    .text               stm32f4_key.o
    0x0800e7e8   0x0800e7e8   0x00000040   Code   RO         1114    .text               led.o
    0x0800e828   0x0800e828   0x000001fc   Code   RO         1160    .text               timer.o
    0x0800ea24   0x0800ea24   0x00000708   Code   RO         1212    .text               adc.o
    0x0800f12c   0x0800f12c   0x00001444   Code   RO         1286    .text               lcd.o
    0x08010570   0x08010570   0x000028a8   Code   RO         1324    .text               lcd_ex.o
    0x08012e18   0x08012e18   0x0000035c   Code   RO         1344    .text               ad9833.o
    0x08013174   0x08013174   0x0000125c   Code   RO         1397    .text               dac.o
    0x080143d0   0x080143d0   0x000000f0   Code   RO         1453    .text               arm_cortexM4lf_math.lib(arm_cmplx_mag_f32.o)
    0x080144c0   0x080144c0   0x000006d6   Code   RO         1498    .text               arm_cortexM4lf_math.lib(arm_cfft_f32.o)
    0x08014b96   0x08014b96   0x00000002   PAD
    0x08014b98   0x08014b98   0x000000c0   Code   RO         1542    .text               arm_cortexM4lf_math.lib(arm_cfft_radix4_init_f32.o)
    0x08014c58   0x08014c58   0x000004dc   Code   RO         1637    .text               arm_cortexM4lf_math.lib(arm_cfft_radix8_f32.o)
    0x08015134   0x08015134   0x00000002   Code   RO         1657    .text               c_w.l(use_no_semi_2.o)
    0x08015136   0x08015136   0x00000002   PAD
    0x08015138   0x08015138   0x00000018   Code   RO         1663    .text               c_w.l(noretval__2printf.o)
    0x08015150   0x08015150   0x00000028   Code   RO         1665    .text               c_w.l(noretval__2sprintf.o)
    0x08015178   0x08015178   0x00000052   Code   RO         1669    .text               c_w.l(_printf_str.o)
    0x080151ca   0x080151ca   0x00000002   PAD
    0x080151cc   0x080151cc   0x00000078   Code   RO         1671    .text               c_w.l(_printf_dec.o)
    0x08015244   0x08015244   0x0000010e   Code   RO         1681    .text               c_w.l(__printf_wp.o)
    0x08015352   0x08015352   0x0000003e   Code   RO         1697    .text               c_w.l(strlen.o)
    0x08015390   0x08015390   0x00000064   Code   RO         1699    .text               c_w.l(rt_memcpy_w.o)
    0x080153f4   0x080153f4   0x00000080   Code   RO         1701    .text               c_w.l(strcmpv7m.o)
    0x08015474   0x08015474   0x00000006   Code   RO         1703    .text               c_w.l(heapauxi.o)
    0x0801547a   0x0801547a   0x00000002   Code   RO         1871    .text               c_w.l(use_no_semi.o)
    0x0801547c   0x0801547c   0x00000016   Code   RO         1874    .text               c_w.l(_rserrno.o)
    0x08015492   0x08015492   0x000000b2   Code   RO         1876    .text               c_w.l(_printf_intcommon.o)
    0x08015544   0x08015544   0x0000041e   Code   RO         1878    .text               c_w.l(_printf_fp_dec.o)
    0x08015962   0x08015962   0x00000002   PAD
    0x08015964   0x08015964   0x00000030   Code   RO         1880    .text               c_w.l(_printf_char_common.o)
    0x08015994   0x08015994   0x0000000a   Code   RO         1882    .text               c_w.l(_sputc.o)
    0x0801599e   0x0801599e   0x0000002c   Code   RO         1884    .text               c_w.l(_printf_char.o)
    0x080159ca   0x080159ca   0x00000002   PAD
    0x080159cc   0x080159cc   0x00000024   Code   RO         1886    .text               c_w.l(_printf_char_file.o)
    0x080159f0   0x080159f0   0x00000008   Code   RO         2010    .text               c_w.l(rt_locale_intlibspace.o)
    0x080159f8   0x080159f8   0x00000008   Code   RO         2015    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08015a00   0x08015a00   0x0000008a   Code   RO         2017    .text               c_w.l(lludiv10.o)
    0x08015a8a   0x08015a8a   0x00000002   PAD
    0x08015a8c   0x08015a8c   0x00000080   Code   RO         2019    .text               c_w.l(_printf_fp_infnan.o)
    0x08015b0c   0x08015b0c   0x000000e4   Code   RO         2023    .text               c_w.l(bigflt0.o)
    0x08015bf0   0x08015bf0   0x00000008   Code   RO         2048    .text               c_w.l(ferror.o)
    0x08015bf8   0x08015bf8   0x00000008   Code   RO         2061    .text               c_w.l(libspace.o)
    0x08015c00   0x08015c00   0x0000004a   Code   RO         2064    .text               c_w.l(sys_stackheap_outer.o)
    0x08015c4a   0x08015c4a   0x00000012   Code   RO         2066    .text               c_w.l(exit.o)
    0x08015c5c   0x08015c5c   0x0000003e   Code   RO         2026    CL$$btod_d2e        c_w.l(btod.o)
    0x08015c9a   0x08015c9a   0x00000046   Code   RO         2028    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08015ce0   0x08015ce0   0x00000060   Code   RO         2027    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08015d40   0x08015d40   0x00000338   Code   RO         2036    CL$$btod_div_common  c_w.l(btod.o)
    0x08016078   0x08016078   0x000000dc   Code   RO         2033    CL$$btod_e2e        c_w.l(btod.o)
    0x08016154   0x08016154   0x0000002a   Code   RO         2030    CL$$btod_ediv       c_w.l(btod.o)
    0x0801617e   0x0801617e   0x0000002a   Code   RO         2029    CL$$btod_emul       c_w.l(btod.o)
    0x080161a8   0x080161a8   0x00000244   Code   RO         2035    CL$$btod_mult_common  c_w.l(btod.o)
    0x080163ec   0x080163ec   0x00000030   Code   RO         1957    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x0801641c   0x0801641c   0x00000026   Code   RO         1959    i.__ARM_fpclassifyf  m_wm.l(fpclassifyf.o)
    0x08016442   0x08016442   0x00000006   PAD
    0x08016448   0x08016448   0x000002d8   Code   RO         1906    i.__hardfp_atan     m_wm.l(atan.o)
    0x08016720   0x08016720   0x00000200   Code   RO         1739    i.__hardfp_atan2    m_wm.l(atan2.o)
    0x08016920   0x08016920   0x00000150   Code   RO         1763    i.__hardfp_cosf     m_wm.l(cosf.o)
    0x08016a70   0x08016a70   0x00000358   Code   RO         1937    i.__hardfp_exp      m_wm.l(exp.o)
    0x08016dc8   0x08016dc8   0x00000014   Code   RO         1775    i.__hardfp_fabs     m_wm.l(fabs.o)
    0x08016ddc   0x08016ddc   0x00000004   PAD
    0x08016de0   0x08016de0   0x000003c4   Code   RO         1781    i.__hardfp_log      m_wm.l(log.o)
    0x080171a4   0x080171a4   0x00000180   Code   RO         1795    i.__hardfp_log10f   m_wm.l(log10f.o)
    0x08017324   0x08017324   0x00000190   Code   RO         1821    i.__hardfp_sinf     m_wm.l(sinf.o)
    0x080174b4   0x080174b4   0x00000004   PAD
    0x080174b8   0x080174b8   0x000001f0   Code   RO         1833    i.__hardfp_sinh     m_wm.l(sinh.o)
    0x080176a8   0x080176a8   0x0000007a   Code   RO         1847    i.__hardfp_sqrt     m_wm.l(sqrt.o)
    0x08017722   0x08017722   0x0000003a   Code   RO         1859    i.__hardfp_sqrtf    m_wm.l(sqrtf.o)
    0x0801775c   0x0801775c   0x000000f8   Code   RO         1975    i.__kernel_poly     m_wm.l(poly.o)
    0x08017854   0x08017854   0x00000004   PAD
    0x08017858   0x08017858   0x00000030   Code   RO         1923    i.__mathlib_dbl_divzero  m_wm.l(dunder.o)
    0x08017888   0x08017888   0x00000014   Code   RO         1924    i.__mathlib_dbl_infnan  m_wm.l(dunder.o)
    0x0801789c   0x0801789c   0x00000014   Code   RO         1925    i.__mathlib_dbl_infnan2  m_wm.l(dunder.o)
    0x080178b0   0x080178b0   0x00000020   Code   RO         1926    i.__mathlib_dbl_invalid  m_wm.l(dunder.o)
    0x080178d0   0x080178d0   0x00000020   Code   RO         1927    i.__mathlib_dbl_overflow  m_wm.l(dunder.o)
    0x080178f0   0x080178f0   0x00000020   Code   RO         1929    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x08017910   0x08017910   0x000004f4   Code   RO         1951    i.__mathlib_expm1   m_wm.l(expm1_i.o)
    0x08017e04   0x08017e04   0x00000014   Code   RO         1961    i.__mathlib_flt_divzero  m_wm.l(funder.o)
    0x08017e18   0x08017e18   0x00000006   Code   RO         1962    i.__mathlib_flt_infnan  m_wm.l(funder.o)
    0x08017e1e   0x08017e1e   0x00000002   PAD
    0x08017e20   0x08017e20   0x00000010   Code   RO         1964    i.__mathlib_flt_invalid  m_wm.l(funder.o)
    0x08017e30   0x08017e30   0x00000010   Code   RO         1967    i.__mathlib_flt_underflow  m_wm.l(funder.o)
    0x08017e40   0x08017e40   0x00000154   Code   RO         1983    i.__mathlib_rredf2  m_wm.l(rredf.o)
    0x08017f94   0x08017f94   0x0000000e   Code   RO         1683    i._is_digit         c_w.l(__printf_wp.o)
    0x08017fa2   0x08017fa2   0x00000010   Code   RO         1908    i.atan              m_wm.l(atan.o)
    0x08017fb2   0x08017fb2   0x00000010   Code   RO         1939    i.exp               m_wm.l(exp.o)
    0x08017fc2   0x08017fc2   0x00000018   Code   RO         1777    i.fabs              m_wm.l(fabs.o)
    0x08017fda   0x08017fda   0x00000002   PAD
    0x08017fdc   0x08017fdc   0x0000002c   Code   RO         2053    locale$$code        c_w.l(lc_numeric_c.o)
    0x08018008   0x08018008   0x00000018   Code   RO         1889    x$fpl$basic         fz_wm.l(basic.o)
    0x08018020   0x08018020   0x00000062   Code   RO         1707    x$fpl$d2f           fz_wm.l(d2f.o)
    0x08018082   0x08018082   0x00000002   PAD
    0x08018084   0x08018084   0x00000150   Code   RO         1709    x$fpl$dadd          fz_wm.l(daddsub_clz.o)
    0x080181d4   0x080181d4   0x00000018   Code   RO         1891    x$fpl$dcmpinf       fz_wm.l(dcmpi.o)
    0x080181ec   0x080181ec   0x000002b0   Code   RO         1716    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x0801849c   0x0801849c   0x00000078   Code   RO         1893    x$fpl$deqf          fz_wm.l(deqf.o)
    0x08018514   0x08018514   0x0000005e   Code   RO         2055    x$fpl$dfix          fz_wm.l(dfix.o)
    0x08018572   0x08018572   0x00000002   PAD
    0x08018574   0x08018574   0x0000005a   Code   RO         1719    x$fpl$dfixu         fz_wm.l(dfixu.o)
    0x080185ce   0x080185ce   0x0000002e   Code   RO         1724    x$fpl$dflt          fz_wm.l(dflt_clz.o)
    0x080185fc   0x080185fc   0x00000026   Code   RO         1723    x$fpl$dfltu         fz_wm.l(dflt_clz.o)
    0x08018622   0x08018622   0x00000002   PAD
    0x08018624   0x08018624   0x00000078   Code   RO         1729    x$fpl$dleqf         fz_wm.l(dleqf.o)
    0x0801869c   0x0801869c   0x00000154   Code   RO         1731    x$fpl$dmul          fz_wm.l(dmul.o)
    0x080187f0   0x080187f0   0x0000009c   Code   RO         1895    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x0801888c   0x0801888c   0x0000000c   Code   RO         1897    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x08018898   0x08018898   0x0000006c   Code   RO         1733    x$fpl$drleqf        fz_wm.l(drleqf.o)
    0x08018904   0x08018904   0x00000016   Code   RO         1710    x$fpl$drsb          fz_wm.l(daddsub_clz.o)
    0x0801891a   0x0801891a   0x00000002   PAD
    0x0801891c   0x0801891c   0x00000198   Code   RO         1899    x$fpl$dsqrt         fz_wm.l(dsqrt_umaal.o)
    0x08018ab4   0x08018ab4   0x000001d4   Code   RO         1711    x$fpl$dsub          fz_wm.l(daddsub_clz.o)
    0x08018c88   0x08018c88   0x00000056   Code   RO         1735    x$fpl$f2d           fz_wm.l(f2d.o)
    0x08018cde   0x08018cde   0x0000008c   Code   RO         1901    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x08018d6a   0x08018d6a   0x0000000a   Code   RO         2123    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08018d74   0x08018d74   0x0000000a   Code   RO         1903    x$fpl$fretinf       fz_wm.l(fretinf.o)
    0x08018d7e   0x08018d7e   0x00000004   Code   RO         1737    x$fpl$printf1       fz_wm.l(printf1.o)
    0x08018d82   0x08018d82   0x00000000   Code   RO         1905    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x08018d82   0x08018d82   0x00000002   PAD
    0x08018d84   0x08018d84   0x000000c2   Data   RO            5    .constdata          main.o
    0x08018e46   0x08018e46   0x00002f80   Data   RO         1288    .constdata          lcd.o
    0x0801bdc6   0x0801bdc6   0x00000002   PAD
    0x0801bdc8   0x0801bdc8   0x00000005   Data   RO         1345    .constdata          ad9833.o
    0x0801bdcd   0x0801bdcd   0x00000003   PAD
    0x0801bdd0   0x0801bdd0   0x00000078   Data   RO         1399    .constdata          dac.o
    0x0801be48   0x0801be48   0x00000800   Data   RO         1562    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0801c648   0x0801c648   0x00000080   Data   RO         1563    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0801c6c8   0x0801c6c8   0x00000100   Data   RO         1564    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0801c7c8   0x0801c7c8   0x00000200   Data   RO         1565    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0801c9c8   0x0801c9c8   0x00000400   Data   RO         1566    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0801cdc8   0x0801cdc8   0x00000800   Data   RO         1567    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0801d5c8   0x0801d5c8   0x00001000   Data   RO         1568    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0801e5c8   0x0801e5c8   0x00002000   Data   RO         1569    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x080205c8   0x080205c8   0x00004000   Data   RO         1570    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x080245c8   0x080245c8   0x00008000   Data   RO         1571    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0802c5c8   0x0802c5c8   0x00000028   Data   RO         1576    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0802c5f0   0x0802c5f0   0x00000060   Data   RO         1577    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0802c650   0x0802c650   0x00000070   Data   RO         1578    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0802c6c0   0x0802c6c0   0x000001a0   Data   RO         1579    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0802c860   0x0802c860   0x00000370   Data   RO         1580    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0802cbd0   0x0802cbd0   0x00000380   Data   RO         1581    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0802cf50   0x0802cf50   0x00000e10   Data   RO         1582    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0802dd60   0x0802dd60   0x00001dc0   Data   RO         1583    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0802fb20   0x0802fb20   0x00001f80   Data   RO         1584    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x08031aa0   0x08031aa0   0x00000038   Data   RO         1784    .constdata          m_wm.l(log.o)
    0x08031ad8   0x08031ad8   0x00000040   Data   RO         1798    .constdata          m_wm.l(log10f.o)
    0x08031b18   0x08031b18   0x00000008   Data   RO         1836    .constdata          m_wm.l(sinh.o)
    0x08031b20   0x08031b20   0x00000098   Data   RO         1909    .constdata          m_wm.l(atan.o)
    0x08031bb8   0x08031bb8   0x00000058   Data   RO         1940    .constdata          m_wm.l(exp.o)
    0x08031c10   0x08031c10   0x00000028   Data   RO         1952    .constdata          m_wm.l(expm1_i.o)
    0x08031c38   0x08031c38   0x00000008   Data   RO         1977    .constdata          m_wm.l(qnan.o)
    0x08031c40   0x08031c40   0x00000020   Data   RO         1984    .constdata          m_wm.l(rredf.o)
    0x08031c60   0x08031c60   0x00000094   Data   RO         2024    .constdata          c_w.l(bigflt0.o)
    0x08031cf4   0x08031cf4   0x00000d56   Data   RO            6    .conststring        main.o
    0x08032a4a   0x08032a4a   0x00000002   PAD
    0x08032a4c   0x08032a4c   0x00000048   Data   RO         1400    .conststring        dac.o
    0x08032a94   0x08032a94   0x00000020   Data   RO         2196    Region$$Table       anon$$obj.o
    0x08032ab4   0x08032ab4   0x0000001c   Data   RO         2052    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08032ad0, Size: 0x0001c598, Max: 0x00020000, ABSOLUTE, COMPRESSED[0x00000090])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000180   Data   RW            7    .data               main.o
    0x20000180   COMPRESSED   0x00000014   Data   RW          239    .data               system_stm32f4xx.o
    0x20000194   COMPRESSED   0x00000010   Data   RW          810    .data               stm32f4xx_rcc.o
    0x200001a4   COMPRESSED   0x00000004   Data   RW         1015    .data               delay.o
    0x200001a8   COMPRESSED   0x0000000a   Data   RW         1065    .data               usart.o
    0x200001b2   COMPRESSED   0x00000002   PAD
    0x200001b4   COMPRESSED   0x00000004   Data   RW         1161    .data               timer.o
    0x200001b8   COMPRESSED   0x00000027   Data   RW         1214    .data               adc.o
    0x200001df   COMPRESSED   0x00000001   PAD
    0x200001e0   COMPRESSED   0x0000003c   Data   RW         1257    .data               fft.o
    0x2000021c   COMPRESSED   0x00000008   Data   RW         1289    .data               lcd.o
    0x20000224   COMPRESSED   0x00000015   Data   RW         1401    .data               dac.o
    0x20000239   COMPRESSED   0x00000003   PAD
    0x2000023c        -       0x00005990   Zero   RW            4    .bss                main.o
    0x20005bcc        -       0x000000c8   Zero   RW         1064    .bss                usart.o
    0x20005c94        -       0x00006000   Zero   RW         1213    .bss                adc.o
    0x2000bc94        -       0x00010090   Zero   RW         1256    .bss                fft.o
    0x2001bd24        -       0x0000000e   Zero   RW         1287    .bss                lcd.o
    0x2001bd32        -       0x00000200   Zero   RW         1398    .bss                dac.o
    0x2001bf32   COMPRESSED   0x00000002   PAD
    0x2001bf34        -       0x00000060   Zero   RW         2062    .bss                c_w.l(libspace.o)
    0x2001bf94   COMPRESSED   0x00000004   PAD
    0x2001bf98        -       0x00000200   Zero   RW          263    HEAP                startup_stm32f40_41xxx.o
    0x2001c198        -       0x00000400   Zero   RW          262    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       860         28          5          0          0       5245   ad9833.o
      1800        172          0         39      24576       5536   adc.o
      4700       1268        192         21        512      11729   dac.o
       260          8          0          4          0       1541   delay.o
         0          0          0         60      65680       5147   fft.o
      5188        126      12160          8         14      15245   lcd.o
     10408          0          0          0          0       7209   lcd_ex.o
        64          4          0          0          0        555   led.o
     45972      15548       3608        384      22928     362953   main.o
       224         20          0          0          0     244941   misc.o
        64         26        392          0       1536        864   startup_stm32f40_41xxx.o
       564         16          0          0          0        921   stm32f4_key.o
      1124         24          0          0          0      10746   stm32f4xx_adc.o
       528          6          0          0          0       5097   stm32f4xx_dac.o
       936         32          0          0          0       6453   stm32f4xx_dma.o
       660         44          0          0          0       4217   stm32f4xx_gpio.o
        26          0          0          0          0       1250   stm32f4xx_it.o
      1628         52          0         16          0      13124   stm32f4xx_rcc.o
      3234         60          0          0          0      23072   stm32f4xx_tim.o
      1108         34          0          0          0       7940   stm32f4xx_usart.o
      1400         42          0          0          0       6719   sys.o
       528         46          0         20          0       1867   system_stm32f4xx.o
       508         46          0          4          0       2344   timer.o
       348         20          0         10        200       3378   usart.o

    ----------------------------------------------------------------------
     82136      <USER>      <GROUP>        572     115448     748093   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         4          0          7          6          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       106          0          0          0          0        420   arm_bitreversal2.o
      1750          0          0          0          0       3469   arm_cfft_f32.o
       192         46          0          0          0        767   arm_cfft_radix4_init_f32.o
      1244          4          0          0          0       2339   arm_cfft_radix8_f32.o
       240          4          0          0          0      16312   arm_cmplx_mag_f32.o
         0          0      89176          0          0       2517   arm_common_tables.o
        90          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
       284          0          0          0          0        156   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        22          0          0          0          0        100   _rserrno.o
        10          0          0          0          0         68   _sputc.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        22          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
        24          4          0          0          0         84   noretval__2printf.o
        40          6          0          0          0         84   noretval__2sprintf.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        62          0          0          0          0         76   strlen.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        24          0          0          0          0        164   basic.o
        98          4          0          0          0        140   d2f.o
       826         16          0          0          0        492   daddsub_clz.o
        24          0          0          0          0        116   dcmpi.o
       688        140          0          0          0        256   ddiv.o
       120          4          0          0          0        140   deqf.o
        94          4          0          0          0        140   dfix.o
        90          4          0          0          0        140   dfixu.o
        84          0          0          0          0        232   dflt_clz.o
       120          4          0          0          0        140   dleqf.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
       108          0          0          0          0        128   drleqf.o
       408         56          0          0          0        168   dsqrt_umaal.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
        10          0          0          0          0        116   fretinf.o
         4          0          0          0          0        116   printf1.o
         0          0          0          0          0          0   usenofp.o
       744        106        152          0          0        352   atan.o
       512         64          0          0          0        208   atan2.o
       336         56          0          0          0        136   cosf.o
       184         44          0          0          0        744   dunder.o
       872        142         88          0          0        412   exp.o
      1268        176         40          0          0        264   expm1_i.o
        44          0          0          0          0        248   fabs.o
        48          0          0          0          0        124   fpclassify.o
        38          0          0          0          0        116   fpclassifyf.o
        58         18          0          0          0        464   funder.o
       964         92         56          0          0        316   log.o
       384         52         64          0          0        140   log10f.o
       248          0          0          0          0        152   poly.o
         0          0          8          0          0          0   qnan.o
       340         24         32          0          0        160   rredf.o
       400         56          0          0          0        212   sinf.o
       496         70          8          0          0        192   sinh.o
       122          0          0          0          0        148   sqrt.o
        58          0          0          0          0        136   sqrtf.o

    ----------------------------------------------------------------------
     19236       <USER>      <GROUP>          0        100      36896   Library Totals
        48          4          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      3532         54      89176          0          0      25824   arm_cortexM4lf_math.lib
      5098        214        176          0         96       3272   c_w.l
      3442        256          0          0          0       3276   fz_wm.l
      7116        900        448          0          0       4524   m_wm.l

    ----------------------------------------------------------------------
     19236       <USER>      <GROUP>          0        100      36896   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

    101372      19050     106196        572     115548     772069   Grand Totals
    101372      19050     106196        144     115548     772069   ELF Image Totals (compressed)
    101372      19050     106196        144          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)               207568 ( 202.70kB)
    Total RW  Size (RW Data + ZI Data)            116120 ( 113.40kB)
    Total ROM Size (Code + RO Data + RW Data)     207712 ( 202.84kB)

==============================================================================

