# IIR滤波器功能说明

## 功能概述

基于第二次扫频得到的电压幅度比，自动计算滤波器参数并实现IIR滤波器，实现ADC3采样 → IIR滤波 → DAC输出的完整信号处理链路。

## 实现原理

### 1. 滤波器参数计算

#### 输入数据
- 使用第二次扫频的电压幅度比数据（`sweep_phase2_data[]`）
- 频率范围：1kHz - 400kHz，步进200Hz
- 数据点：最多30个关键频率点

#### 参数计算方法
1. **增益分析**：
   - 最大增益：`max_gain`
   - 最小增益：`min_gain`
   - -3dB阈值：`0.707 × max_gain`

2. **截止频率计算**：
   - 低截止频率：第一个达到-3dB阈值的频率点
   - 高截止频率：最后一个达到-3dB阈值的频率点
   - 带宽：`bandwidth = freq_high_3db - freq_low_3db`

3. **中心频率**：最大增益对应的频率

4. **品质因数**：`Q = center_freq / bandwidth`

5. **滤波器类型判断**：
   - **低通**：低频增益 > 0.707×max_gain，高频增益 < 0.707×max_gain
   - **高通**：低频增益 < 0.707×max_gain，高频增益 > 0.707×max_gain  
   - **带通**：低频和高频增益都 < 0.707×max_gain
   - **带阻**：低频和高频增益都 > 0.707×max_gain

### 2. IIR滤波器设计

#### 滤波器结构
- **类型**：二阶IIR滤波器（Direct Form II）
- **系数**：b0, b1, b2（前向），a1, a2（反馈）
- **延迟线**：x1, x2（输入），y1, y2（输出）

#### 设计方法
使用双线性变换（Bilinear Transform）设计：

1. **低通滤波器**：
   ```
   ω = 2π × fc / fs
   α = sin(ω) / (2Q)
   b0 = (1 - cos(ω)) / (2(1 + α))
   b1 = (1 - cos(ω)) / (1 + α)  
   b2 = (1 - cos(ω)) / (2(1 + α))
   a1 = -2cos(ω) / (1 + α)
   a2 = (1 - α) / (1 + α)
   ```

2. **高通滤波器**：
   ```
   b0 = (1 + cos(ω)) / (2(1 + α))
   b1 = -(1 + cos(ω)) / (1 + α)
   b2 = (1 + cos(ω)) / (2(1 + α))
   ```

3. **带通滤波器**：
   ```
   b0 = α / (1 + α)
   b1 = 0
   b2 = -α / (1 + α)
   ```

4. **带阻滤波器**：
   ```
   b0 = 1 / (1 + α)
   b1 = -2cos(ω) / (1 + α)
   b2 = 1 / (1 + α)
   ```

### 3. 实时滤波处理

#### 处理流程
1. **ADC3采样**：102.314 kHz采样率，512点
2. **直流偏置去除**：计算并去除输入信号的直流分量
3. **IIR滤波**：逐点处理，使用Direct Form II结构
4. **信号重构**：加回直流偏置，转换为DAC格式
5. **DAC输出**：根据滤波器中心频率确定输出频率

#### 滤波算法
```c
float ProcessIIRFilter(IIRFilter* filter, float input)
{
    float output = filter->b0 * input + filter->b1 * filter->x1 + filter->b2 * filter->x2
                   - filter->a1 * filter->y1 - filter->a2 * filter->y2;
    
    // 更新延迟线
    filter->x2 = filter->x1;
    filter->x1 = input;
    filter->y2 = filter->y1;
    filter->y1 = output;
    
    return output;
}
```

## 使用流程

### 1. 准备工作
1. **运行扫频测试**：按PE2键启动完整的三阶段扫频
2. **等待完成**：确保第二次扫频数据已保存

### 2. 启动IIR滤波
1. **按下按钮八**：启动IIR滤波处理
2. **自动计算**：系统自动计算滤波器参数
3. **滤波器初始化**：根据参数设计IIR滤波器
4. **开始处理**：ADC3采样 → IIR滤波 → DAC输出

### 3. 监控输出
- **串口输出**：详细的参数计算和滤波过程信息
- **LCD显示**：当前状态和处理进度
- **DAC输出**：滤波后的模拟信号

## 关键数据结构

### FilterParameters
```c
typedef struct {
    FilterType type;        // 滤波器类型
    float center_freq;      // 中心频率 (Hz)
    float cutoff_freq_low;  // 低截止频率 (Hz)
    float cutoff_freq_high; // 高截止频率 (Hz)
    float quality_factor;   // 品质因数 Q
    float bandwidth;        // 带宽 (Hz)
    float gain_max;         // 最大增益
    float gain_min;         // 最小增益
} FilterParameters;
```

### IIRFilter
```c
typedef struct {
    float b0, b1, b2;  // 前向系数
    float a1, a2;      // 反馈系数
    float x1, x2;      // 输入延迟
    float y1, y2;      // 输出延迟
} IIRFilter;
```

## 关键函数

- `CalculateFilterParameters()`: 计算滤波器参数
- `InitializeIIRFilter()`: 初始化IIR滤波器
- `ProcessIIRFilter()`: 处理单个样本
- `ADC3_IIRFilter_Process()`: 启动IIR滤波处理
- `ADC3_ProcessIIRFilter()`: 执行完整的滤波处理

## 性能特点

### 优势
1. **自适应**：根据实际测量的滤波器特性自动设计
2. **实时**：基于中断的实时滤波处理
3. **精确**：使用浮点运算保证精度
4. **完整**：包含完整的ADC→滤波→DAC链路

### 技术指标
- **采样率**：102.314 kHz
- **滤波器阶数**：二阶IIR
- **频率范围**：1kHz - 50kHz（受采样率限制）
- **精度**：12位ADC/DAC，浮点运算

## 调试信息

系统会输出详细的调试信息：
```
=== 滤波器参数计算 ===
最大增益: 1.200, 最小增益: 0.050
中心频率: 5000.0 Hz
-3dB增益阈值: 0.849
低截止频率: 3000.0 Hz
高截止频率: 7000.0 Hz
带宽: 4000.0 Hz
品质因数Q: 1.25
滤波器类型: 带通滤波器

=== IIR滤波器初始化 ===
带通滤波器: fc=5000.0 Hz, BW=4000.0 Hz, Q=1.25
IIR系数: b0=0.123456, b1=0.000000, b2=-0.123456, a1=-1.234567, a2=0.765432

=== ADC3 IIR滤波处理 ===
输入信号直流偏置: 2048.0 (1.650 V)
滤波后信号特性:
  峰峰值: 0.500000 V
  最大值: 0.250000 V
  最小值: -0.250000 V
DAC输出参数:
  目标频率: 5000.0 Hz
  实际频率: 5115.7 Hz
  每周期点数: 20
  DAC采样率: 102314 Hz
```

## 注意事项

1. **必须先完成扫频**：IIR滤波功能依赖第二次扫频数据
2. **频率限制**：受ADC采样率限制，有效频率范围为1kHz-50kHz
3. **稳定性**：IIR滤波器可能存在稳定性问题，系统会自动检查系数
4. **内存使用**：滤波处理需要额外的内存缓冲区
5. **实时性**：滤波处理为批处理模式，不是连续实时滤波
