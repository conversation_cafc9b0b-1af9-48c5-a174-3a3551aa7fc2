#include "stm32f4xx.h"
#include "stdio.h"
#include "usart.h"
#include "delay.h"
#include "led.h"
#include "G.h"
#include "timer.h"
#include "math.h"
#include "arm_math.h"
#include "arm_const_structs.h"
#include "kalman.h"
#include "fft.h"
#include "adc.h"
#include "dac.h"
#include "AD9833.h"
#include "lcd.h"
#include "stm32f4_key.h"
#include "touch.h"
#include <stdbool.h>
#include <string.h>
#include <stdlib.h>

// 定义PI常数（如果编译器没有定义）
#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

// Function prototypes match the declarations in adc.h
// 注意：ADC1和ADC2不再进行FFT处理 - 以下函数已被禁用
void QCZ_FFT(volatile uint16_t* buff);    // 已禁用 - ADC1不再进行FFT
void QCZ_FFT1(volatile uint16_t* buff);   // 已禁用 - ADC2不再进行FFT

// Global variables from your project
bool Separate = false;
extern int t;
extern float fft_outputbuf[FFT_LENGTH];
extern u8 Res;
uint32_t frequency_A, frequency_B;
int phase_difference_A;
int phase_difference_B;
int phase_difference_A1;
int phase_difference_B1;
extern float phase_A, phase_B, phase;
extern float frequency;
double current_output_freq_A, current_output_freq_B;
float phase_A_CS = 0.0f;
float phase_B_CS = 0.0f;
float phase_A_SX = 0.0f;
float phase_B_SX = 0.0f;
uint16_t current_phase_B = 0;
uint32_t peak_idx;
extern uint32_t peak1_idx, peak2_idx;

// Extern declarations now match the original types in your header files
extern volatile uint16_t buff_adc[];
extern volatile uint16_t buff_adc2[];
extern volatile uint16_t buff_adc3[];

// CORRECTED: Declarations are now on separate lines to match adc.h
extern volatile u8 flag_ADC;
extern volatile u8 flag_ADC1;
extern volatile u8 flag_ADC2;

extern float sampfre;
extern arm_cfft_radix4_instance_f32 scfft;

u8 QCZ = 100;
u8 QCZ1 = 0;
int QCZ_Phase[2];
int QCZ_Phase1[2];
int Phase = 0;
float ZE;
int SBP = 0;

uint16_t waveform_A, waveform_B;
uint16_t waveform_A_prime, waveform_B_prime;

char lcd_buffer[50];

// 频率显示格式化函数
void format_frequency_display(float freq, char* buffer) {
    if (freq >= 1000000.0f) {
        // 显示为MHz
        sprintf(buffer, "%.2f MHz", freq / 1000000.0f);
    } else if (freq >= 1000.0f) {
        // 显示为kHz
        sprintf(buffer, "%.1f kHz", freq / 1000.0f);
    } else {
        // 显示为Hz
        sprintf(buffer, "%.0f Hz", freq);
    }
}

// 频率控制变量
float current_frequency = 100.0;  // 当前频率，从100Hz开始
uint8_t key0_pressed = 0;         // PE4按键按下标志
uint8_t key1_pressed = 0;         // PE3按键按下标志
uint8_t key2_pressed = 0;         // PE2按键按下标志（扫频测试）
uint8_t wk_pressed = 0;           // PA0按键按下标志（ADC3处理）
uint8_t frequency_changed = 1;    // 频率改变标志，用于更新显示
uint8_t dac_multiplier_changed = 1; // DAC倍数改变标志
uint8_t dac_enable_changed = 1;   // DAC使能改变标志
uint8_t adc_enable_changed = 1;   // ADC使能改变标志
uint8_t adc_user_enabled = 0;     // ADC用户使能标志（按钮控制）
// DAC使能状态通过DAC模块的dac_user_enabled变量控制
uint8_t selected_button = 0;      // 当前选中的按钮索引

// ADC1采样数据存储 - 进一步优化内存使用
#define ADC1_SAMPLE_SIZE 512  // 进一步减少到512，节省更多内存
uint16_t adc1_sample_buffer[ADC1_SAMPLE_SIZE];  // ADC1采样数据缓冲区
volatile uint16_t adc1_sample_index = 0;       // 当前采样索引
volatile uint8_t adc1_sampling_complete = 0;   // 采样完成标志

// ADC2采样数据存储 - 进一步优化内存使用
#define ADC2_SAMPLE_SIZE 512  // 进一步减少到512，节省更多内存
uint16_t adc2_sample_buffer[ADC2_SAMPLE_SIZE];  // ADC2采样数据缓冲区
volatile uint16_t adc2_sample_index = 0;       // 当前采样索引
volatile uint8_t adc2_sampling_complete = 0;   // 采样完成标志

// ADC3采样数据存储 - 恢复为512点
#define ADC3_SAMPLE_SIZE 512   // ADC3使用512点进行FFT幅频谱分析
uint16_t adc3_sample_buffer[ADC3_SAMPLE_SIZE];  // ADC3采样数据缓冲区
volatile uint16_t adc3_sample_index = 0;       // 当前采样索引
volatile uint8_t adc3_sampling_complete = 0;   // 采样完成标志
volatile uint8_t adc3_user_enabled = 0;        // ADC3用户使能标志

// ADC3 FFT相关变量
float adc3_fft_inputbuf[ADC3_SAMPLE_SIZE * 2];  // FFT输入缓冲区（复数，实部+虚部）
float adc3_fft_outputbuf[ADC3_SAMPLE_SIZE];     // FFT输出缓冲区（幅度谱）
float adc3_dc_removed[ADC3_SAMPLE_SIZE];        // 去直流后的数据缓冲区

// 反FFT和DAC输出相关变量 - 使用512点IFFT
float adc3_ifft_inputbuf[ADC3_SAMPLE_SIZE * 2]; // 反FFT输入缓冲区（复数）
float adc3_ifft_512_buf[512 * 2];               // 512点IFFT缓冲区（复数）
uint16_t adc3_reconstructed[512];               // 重构后的DAC输出数据（512点）

// DAC输出优化参数（限制采样率≤512kHz）
float adc3_fundamental_freq = 0.0f;             // 检测到的基波频率
uint16_t adc3_points_per_cycle = 100;           // 一个周期内的输出点数（默认100，范围8-256）
float adc3_dac_sample_rate = 512000.0f;         // DAC输出采样率（上限512kHz）
uint16_t adc3_cycle_buffer[512];                // 一个周期的数据缓冲区（最大512点）

// 第二次扫频结果存储
#define MAX_SWEEP_POINTS 50   // 进一步减少到50点以节省内存
typedef struct {
    float frequency;        // 频率 (Hz)
    float voltage_ratio;    // 电压幅度比
    bool valid;            // 数据有效标志
} SweepPoint;

SweepPoint sweep_correction_data[MAX_SWEEP_POINTS];     // 扫频校正数据数组
int sweep_correction_count = 0;                         // 有效扫频校正点数
bool use_sweep_correction = false;                      // 是否使用扫频校正

// 第二次扫频的完整数据存储（用于第八个按钮功能）
// 使用较小的数组，只保存关键频率点
#define MAX_PHASE2_POINTS 30  // 只保存30个关键点
SweepPoint sweep_phase2_data[MAX_PHASE2_POINTS];        // 第二次扫频关键数据
int sweep_phase2_count = 0;                             // 第二次扫频数据点数
bool sweep_phase2_completed = false;                    // 第二次扫频是否完成

// ADC3超时保护
volatile uint32_t adc3_timeout_counter = 0;     // 超时计数器
#define ADC3_TIMEOUT_MS 5000                     // 5秒超时

// 外部DAC变量声明
extern uint8_t dac_output_enabled;             // DAC正弦波输出使能（来自dac.c）

char lcd_buffer[50];              // LCD显示缓冲区

// ADC1采样控制函数声明
void ADC1_StartSampling(void);
void ADC1_StopSampling(void);
void ADC1_ResetSampling(void);

// ADC2采样控制函数声明
void ADC2_StartSampling(void);
void ADC2_StopSampling(void);
void ADC2_ResetSampling(void);

// ADC3采样控制函数声明
void ADC3_StartSampling(void);
void ADC3_StopSampling(void);
void ADC3_ResetSampling(void);
void ADC3_ProcessFFT(void);
void ADC3_ReconstructSignal(void);
void ADC3_ReconstructSignal_Simplified(void);  // 简化版本
void ADC3_GenerateCleanSineWave(void);          // 生成清洁正弦波

void ADC3_CalculateOptimalDACParams(float fundamental_freq);
void ADC3_StartOptimizedDACOutput(void);
void ADC3_HarmonicFiltering(int max_freq_bin, float freq_resolution);
void ADC3_OutputFFTResults(void);  // 专门输出FFT分析结果的函数
void ADC3_ReconstructStandardSineWave(void);  // 三次以内谐波重构标准正弦波
void ADC3_IntelligentReconstruction(void);  // 智能重构：根据基频判断输出方式
void ADC3_ReconstructPureSineWave(float fundamental_freq, float fundamental_amplitude);  // 重构纯正弦波
void ADC3_ReconstructComplexWaveform(void);  // 重构复杂波形
void ADC3_ReconstructStandardSineWave_2VPP(float fundamental_freq);  // 重构2V峰峰值标准正弦波（相位置零）

// 扫频校正相关函数
void ADC3_LoadSweepResults(void);                                    // 加载扫频结果
void ADC3_AddSweepPoint(float frequency, float voltage_ratio);       // 添加扫频点
float ADC3_GetSweepRatio(float frequency);                          // 获取指定频率的电压比
void ADC3_ApplySweepCorrection(void);                               // 应用扫频校正到FFT结果
void ADC3_SetSweepData(float* frequencies, float* ratios, int count);  // 快速设置扫频数据

// 第二次扫频数据管理函数
void SaveSweepPhase2Data(float frequency, float voltage_ratio);      // 保存第二次扫频数据
float GetSweepPhase2Ratio(float frequency);                         // 获取第二次扫频的电压比
void ClearSweepPhase2Data(void);                                    // 清空第二次扫频数据
void LoadSweepPhase2ToCorrection(void);                             // 将第二次扫频数据加载到校正数组



// 扫频测试相关变量和函数声明
typedef struct {
    float frequency;        // 当前频率
    float adc1_amplitude;   // ADC1幅度（滤波器输入）
    float adc2_amplitude;   // ADC2幅度（滤波器输出）
    float voltage_ratio;    // 电压幅度比（输出/输入）
} FrequencyResponse;

#define SWEEP_POINTS 1996   // 扫频点数：(400kHz-1kHz)/200Hz + 1 = 1996
#define SWEEP_POINTS_100K 496  // 1kHz到100kHz的扫频点数：(100kHz-1kHz)/200Hz + 1 = 496
#define SWEEP_BUFFER_SIZE 20  // 进一步减少到20个点
#define SMOOTH_FILTER_SIZE 3  // 减少平滑滤波器窗口大小
FrequencyResponse sweep_results[SWEEP_BUFFER_SIZE];  // 进一步减少内存使用：20×20字节=400B
volatile uint8_t sweep_test_active = 0;
volatile uint16_t current_sweep_point = 0;
volatile uint8_t sweep_sampling_complete = 0;
volatile uint16_t total_sweep_points = 0;  // 总扫频点数计数器

// 归一化处理相关变量
float max_voltage_ratio = 0.0f;           // 最大电压幅度比
volatile uint8_t sweep_phase = 0;          // 扫频阶段：0=低频检测，1=完整扫频，2=归一化输出

// 平滑滤波器相关变量
float smooth_buffer_phase2[SMOOTH_FILTER_SIZE];  // 第二次扫频平滑缓冲区
float smooth_buffer_phase3[SMOOTH_FILTER_SIZE];  // 第三次扫频平滑缓冲区
uint8_t smooth_index_phase2 = 0;                 // 第二次扫频平滑缓冲区索引
uint8_t smooth_index_phase3 = 0;                 // 第三次扫频平滑缓冲区索引
uint8_t smooth_count_phase2 = 0;                 // 第二次扫频平滑缓冲区有效数据计数
uint8_t smooth_count_phase3 = 0;                 // 第三次扫频平滑缓冲区有效数据计数

// 滤波器类型判断相关变量
float freq_1kHz_ratio = 0.0f;             // 1kHz频率点的归一化电压幅度比
float freq_1_2kHz_ratio = 0.0f;           // 1.2kHz频率点的归一化电压幅度比
float freq_399_8kHz_ratio = 0.0f;         // 399.8kHz频率点的归一化电压幅度比
float freq_400kHz_ratio = 0.0f;           // 400kHz频率点的归一化电压幅度比

// 第一次扫频的低频检测结果
float first_sweep_1kHz_ratio = 0.0f;      // 第一次扫频1kHz的电压幅度比
float first_sweep_1_2kHz_ratio = 0.0f;    // 第一次扫频1.2kHz的电压幅度比
uint8_t amplitude_multiplier = 1;         // 电压幅度比倍数（1或2）
uint8_t low_freq_points_completed = 0;    // 低频点完成计数

// 扫频测试函数声明
void StartSweepTest(void);
void StopSweepTest(void);
void ProcessSweepPoint(void);
void OutputSweepResults(void);
void DetermineFilterType(void);


// 扫频结果查询函数声明
float GetAmplitudeResponseFromSweep(float frequency);


// 平滑滤波函数声明
float ApplySmoothFilter(float new_value, float* buffer, uint8_t* index, uint8_t* count, uint8_t buffer_size);
void InitSmoothFilters(void);

// 扫频测试函数声明（简化版）

// 虚拟按钮定义
typedef struct {
    uint16_t x;      // 按钮左上角X坐标
    uint16_t y;      // 按钮左上角Y坐标
    uint16_t width;  // 按钮宽度
    uint16_t height; // 按钮高度
    char* text;      // 按钮文字
    float freq_step; // 频率步进值
    uint16_t color;  // 按钮颜色
} Button_t;

// 定义八个按钮 - 更大尺寸便于操作
Button_t buttons[8] = {
    // 第一行：频率调整按钮
    {5,   130, 90, 60, "+100kHz", 100000.0f, BLUE},
    {100, 130, 90, 60, "+10kHz",  10000.0f,  GREEN},
    {195, 130, 90, 60, "+1kHz",   1000.0f,   ORANGE},
    {290, 130, 90, 60, "+100Hz",  100.0f,    RED},
    // 第二行：DAC和ADC控制按钮
    {5,   200, 90, 60, "DAC OFF",  0.0f,     GRAY},     // DAC开关按钮
    {100, 200, 90, 60, "DAC x1.0", 0.0f,     MAGENTA},  // DAC倍数按钮
    {195, 200, 90, 60, "SWEEP OFF", 0.0f,     GRAY},     // 扫频测试按钮
    {290, 200, 90, 60, "PROC OFF", 0.0f,     GRAY}      // ADC3 FFT+IFFT+DAC输出按钮
};

// 绘制按钮函数 - 支持选中和按下状态
void draw_button(Button_t* btn, uint8_t pressed, uint8_t selected) {
    uint16_t bg_color, text_color, border_color;

    if (pressed) {
        // 按下状态：红色背景，白色文字
        bg_color = RED;
        text_color = WHITE;
        border_color = RED;
    } else if (selected) {
        // 选中状态：蓝色边框，黑色文字
        bg_color = WHITE;
        text_color = BLACK;
        border_color = BLUE;
    } else {
        // 正常状态：黑色边框，黑色文字
        bg_color = WHITE;
        text_color = BLACK;
        border_color = BLACK;
    }

    // 绘制按钮背景
    lcd_fill(btn->x, btn->y, btn->x + btn->width, btn->y + btn->height, bg_color);

    // 绘制按钮边框
    lcd_draw_rectangle(btn->x, btn->y, btn->x + btn->width, btn->y + btn->height, border_color);

    // 如果是选中状态，绘制双重边框
    if (selected && !pressed) {
        lcd_draw_rectangle(btn->x + 1, btn->y + 1, btn->x + btn->width - 1, btn->y + btn->height - 1, border_color);
    }

    // 计算文字居中位置
    uint16_t text_len = strlen(btn->text);
    uint16_t text_x = btn->x + (btn->width - text_len * 6) / 2;  // 16号字体宽度约6像素
    uint16_t text_y = btn->y + (btn->height - 16) / 2;          // 16号字体高度16像素

    // 保存当前画笔颜色
    uint32_t old_color = g_point_color;

    // 设置文字颜色并显示按钮文字
    g_point_color = text_color;
    lcd_show_string(text_x, text_y, btn->width, btn->height, 16, btn->text, text_color);

    // 恢复画笔颜色
    g_point_color = old_color;
}

// 绘制所有按钮
void draw_all_buttons(uint8_t selected_index) {
    for (int i = 0; i < 8; i++) {
        draw_button(&buttons[i], 0, (i == selected_index) ? 1 : 0);
    }
}

// 检测按钮点击
int check_button_press(uint16_t touch_x, uint16_t touch_y) {
    for (int i = 0; i < 8; i++) {
        if (touch_x >= buttons[i].x && touch_x <= (buttons[i].x + buttons[i].width) &&
            touch_y >= buttons[i].y && touch_y <= (buttons[i].y + buttons[i].height)) {
            return i;  // 返回按钮索引
        }
    }
    return -1;  // 没有按钮被按下
}



// 频率调整函数
void adjust_frequency(float step) {
    current_frequency += step;

    // 检查频率范围
    if (current_frequency > 1200000.0f) {
        current_frequency = 100.0f;  // 回到100Hz
    } else if (current_frequency < 100.0f) {
        current_frequency = 100.0f;  // 最小100Hz
    }

    // 设置AD9833新的频率
    AD9833_SetFrequencyQuick1(current_frequency, AD9833_OUT_SINUS1);

    // 只有在DAC开启时才设置DAC正弦波频率
    if (DAC_GetUserEnable()) {
        DAC_SetSineFrequency(current_frequency);
    }

    frequency_changed = 1;
}

int main(void)
{
    arm_cfft_radix4_init_f32(&scfft, FFT_LENGTH, 0, 1);
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    uart_init(112500);
    delay_init(168);

    // 串口测试输出
    printf("System Starting...\r\n");
    printf("Key Functions:\r\n");
    printf("  PE4: Navigate buttons\r\n");
    printf("  PE3: Press selected button\r\n");
    printf("  PE2: One-key Sweep Test (Button 7)\r\n");
    printf("  PA0: One-key ADC3 Processing (Button 8)\r\n");
    delay_ms(100);

    LED_Init();
    Adc_Init();
    Adc2_Init();     // ADC2配置为PC1引脚（ADC123_IN11通道11）
    DAC_PA4_Init();  // PA4配置为DAC而不是ADC2
    DAC_SineWave_Init();  // 初始化DAC正弦波功能
    DAC_SetUserEnable(0); // 初始状态DAC用户禁用

    Adc3_Init();

    // 初始状态关闭ADC
    ADC_Cmd(ADC1, DISABLE);
    ADC_Cmd(ADC2, DISABLE);
    ADC_Cmd(ADC3, DISABLE);
    // 扫频测试使用中断方式采样，不需要DMA
    // DMA1_Init();  // ADC1使用中断采样，不需要DMA
    // DMA2_Init();  // ADC2使用中断采样，不需要DMA
    // DMA3_Init();  // ADC3也使用中断采样，不需要DMA
    AD9833_Init();
    AD9833_Init1();
    key_config();  // 初始化按键

    lcd_init();
   
    sampfre = 815534;  // 实际采样频率：84MHz / 103 / 1 = 815534Hz

    TIM3_Int_Init(103 - 1, 1 - 1);  // 84MHz / 103 / 1 = 815534Hz ≈ 819200Hz，用于ADC触发
    TIM4_Int_Init(1000 - 1, 8400 - 1);
    TIM_Cmd(TIM3, ENABLE);

    // TIM6用于DAC DMA触发，只在需要时才初始化和启动
    // 不在系统初始化时启动TIM6，避免与后续DAC配置冲突
    // TIM6_DAC_Init(105 - 1, 1 - 1);  // 注释掉，改为按需初始化

    // UI Redesign for better aesthetics and clarity
    lcd_clear(WHITE);
    g_point_color = BLACK;

    // 删除未使用的变量以消除编译警告

    // 设置默认画笔颜色
    g_point_color = BLACK;

    // 显示标题和操作提示
    lcd_show_string(10, 30, lcddev.width, 30, 16, "Frequency_out:", BLACK);

    // 绘制频率控制按钮（默认选中第一个）
    draw_all_buttons(selected_button);


    // 设置AD9833通道一产生100Hz正弦波
    AD9833_SetFrequencyQuick1(current_frequency, AD9833_OUT_SINUS1);

    // 设置DAC输出相同频率的正弦波 (0-1V范围)
    DAC_SetSineFrequency(current_frequency);

    // 立即显示初始频率
    g_point_color = BLACK;
    format_frequency_display(current_frequency, lcd_buffer);
    uint16_t str_len = strlen(lcd_buffer);
    uint16_t x_pos = (lcddev.width - str_len * 8) / 2;
    lcd_show_string(x_pos, 80, lcddev.width, 30, 16, lcd_buffer, BLACK);



    // 显示初始选中的按钮
    sprintf(lcd_buffer, "Selected: %s (%.0f)", buttons[selected_button].text, buttons[selected_button].freq_step);
    lcd_show_string(10, 50, lcddev.width, 20, 12, lcd_buffer, BLUE);

    // 显示按键功能提示
    lcd_show_string(10, 270, lcddev.width, 20, 12, "PE4:Select PE3:Press", WHITE);
    lcd_show_string(10, 290, lcddev.width, 20, 12, "PE2:Sweep PA0:ADC3", WHITE);

    // 标记频率已显示
    frequency_changed = 0;

    while (1)
    {
        // 检测PE4按键（KEY0）- 移动选择按钮
        if (KEY0 == 0)  // 按键按下（低电平有效）
        {
            if (key0_pressed == 0)  // 防止重复触发
            {
                key0_pressed = 1;

                // 移动到下一个按钮
                selected_button = (selected_button + 1) % 8;

                // 重新绘制所有按钮以更新选中状态
                draw_all_buttons(selected_button);

                // 显示当前选中的按钮信息
                sprintf(lcd_buffer, "Selected: %s (%.0f)", buttons[selected_button].text, buttons[selected_button].freq_step);
                lcd_show_string(10, 50, lcddev.width, 20, 12, lcd_buffer, BLUE);

                // 延时防抖
                delay_ms(200);
            }
        }
        else
        {
            key0_pressed = 0;  // 按键释放
        }

        // 检测PE3按键（KEY1）- 按下当前选中的按钮
        if (KEY1 == 0)  // 按键按下（低电平有效）
        {
            if (key1_pressed == 0)  // 防止重复触发
            {
                key1_pressed = 1;

                // 显示按钮按下效果
                draw_button(&buttons[selected_button], 1, 1);
                delay_ms(100);  // 显示按下效果

                // 执行按钮功能
                if (selected_button == 4) {
                    // DAC开关按钮
                    uint8_t current_dac_state = DAC_GetUserEnable();
                    DAC_SetUserEnable(!current_dac_state);
                    dac_enable_changed = 1;

                    // 更新按钮文本和颜色
                    if (DAC_GetUserEnable()) {
                        sprintf(buttons[4].text, "DAC ON");
                        buttons[4].color = GREEN;
                    } else {
                        sprintf(buttons[4].text, "DAC OFF");
                        buttons[4].color = GRAY;
                    }

                    // 调试信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "DAC: %s", DAC_GetUserEnable() ? "ON" : "OFF");
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                } else if (selected_button == 5) {
                    // DAC倍数按钮 - 只有在DAC开启时才能调整
                    if (DAC_GetUserEnable()) {
                        DAC_NextAmplitudeMultiplier();
                        dac_multiplier_changed = 1;

                        // 更新按钮文本
                        float multiplier = DAC_GetAmplitudeMultiplier();
                        sprintf(buttons[5].text, "DAC x%.1f", multiplier);

                        // 调试信息
                        char debug_buffer[100];
                        sprintf(debug_buffer, "DAC Multiplier: %.1f", multiplier);
                        lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);
                    } else {
                        // DAC未开启时的提示
                        char debug_buffer[100];
                        sprintf(debug_buffer, "Please enable DAC first!");
                        lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);
                    }
                } else if (selected_button == 6) {
                    // ADC开关按钮 - 启动扫频测试
                    if (!sweep_test_active) {
                        // 启动扫频测试
                        sprintf(buttons[6].text, "SWEEP ON");
                        buttons[6].color = GREEN;

                        // 启动扫频测试（无串口输出）

                        // 关闭DAC
                        if (DAC_GetUserEnable()) {
                            DAC_SetUserEnable(0);
                            dac_enable_changed = 1;
                            sprintf(buttons[4].text, "DAC OFF");
                            buttons[4].color = GRAY;
                        }

                        // 启动扫频测试
                        StartSweepTest();

                    } else {
                        // 停止扫频测试
                        sprintf(buttons[6].text, "SWEEP OFF");
                        buttons[6].color = GRAY;

                        StopSweepTest();
                    }

                    // 调试信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "ADC: %s", adc_user_enabled ? "ON" : "OFF");
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                } else if (selected_button == 7) {
                    // ADC3 FFT+IFFT+DAC输出一体化按钮
                    if (!adc3_user_enabled) {
                        // 检查是否有第二次扫频数据
                        if (!sweep_phase2_completed) {
                            char debug_buffer[100];
                            sprintf(debug_buffer, "Please run sweep first!");
                            lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);
                            printf("Warning: No sweep phase 2 data available. Please run sweep test first.\r\n");
                        } else {
                            // 启动完整流程：ADC采样 → FFT分析 → 扫频校正 → IFFT重构 → DAC输出
                            sprintf(buttons[7].text, "PROC ON");
                            buttons[7].color = GREEN;
                            adc3_user_enabled = 1;

                            // 启用扫频校正
                            use_sweep_correction = true;
                            printf("Button 8: Sweep correction enabled with %d data points\r\n", sweep_correction_count);
                            printf("Button 8: Will apply correction to <10kHz FFT and >10kHz sine output\r\n");

                            // 停止正弦波输出，为重构信号让路
                            dac_output_enabled = 0;
                            DAC_StopSineOutput();

                            // 配置102.4kHz采样率：84MHz / 821 / 1 = 102,314Hz ≈ 102.4kHz
                            TIM5_ADC3_Init(821 - 1, 1 - 1);
                            TIM_Cmd(TIM5, ENABLE);

                            // 启动ADC3采样
                            ADC3_ResetSampling();
                            ADC3_StartSampling();

                            char debug_buffer[100];
                            sprintf(debug_buffer, "ADC3: Processing with sweep correction...");
                            lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, GREEN);
                        }
                    } else {
                        // 停止完整流程
                        sprintf(buttons[7].text, "PROC OFF");
                        buttons[7].color = GRAY;
                        adc3_user_enabled = 0;

                        // 停止ADC3采样
                        ADC3_StopSampling();

                        // 停止DAC重构信号输出
                        DAC_StopReconstructedOutput();

                        // 停止TIM5（ADC3专用定时器）
                        TIM_Cmd(TIM5, DISABLE);

                        char debug_buffer[100];
                        sprintf(debug_buffer, "ADC3: Stopped");
                        lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);
                    }

                } else {
                    // 频率调整按钮 (0-3)
                    float step_value = buttons[selected_button].freq_step;

                    // 调试信息：显示按钮详细信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "Btn:%d Step:%.0f", selected_button, step_value);
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                    adjust_frequency(step_value);
                }

                // 恢复按钮正常显示
                draw_all_buttons(selected_button);

                // 延时防抖
                delay_ms(200);
            }
        }
        else
        {
            key1_pressed = 0;  // 按键释放
        }

        // 检测PE2按键（KEY2）- 一键启动扫频测试（按钮七）
        if (KEY2 == 0)  // 按键按下（低电平有效）
        {
            if (key2_pressed == 0)  // 防止重复触发
            {
                key2_pressed = 1;

                // 直接启动扫频测试（相当于按下按钮七）
                printf("PE2 Key: Starting Sweep Test\r\n");

                // 显示按钮按下效果
                draw_button(&buttons[6], 1, 1);
                delay_ms(100);  // 显示按下效果

                // 执行扫频测试功能
                if (strcmp(buttons[6].text, "SWEEP OFF") == 0) {
                    // 启动扫频测试
                    sprintf(buttons[6].text, "SWEEP ON");
                    buttons[6].color = GREEN;

                    // 重置扫频状态
                    sweep_phase = 0;
                    current_sweep_point = 0;
                    low_freq_points_completed = 0;

                    // 设置起始频率重新开始扫频
                    float start_freq = 1000.0f;
                    AD9833_SetFrequencyQuick1(start_freq, AD9833_OUT_SINUS1);
                    delay_ms(5);  // 优化：减少延时

                    // 启动ADC1和ADC2采样
                    ADC_Cmd(ADC1, ENABLE);
                    ADC_Cmd(ADC2, ENABLE);
                    TIM3_Int_Init(103 - 1, 1 - 1);  // 配置采样率
                    TIM_Cmd(TIM3, ENABLE);

                    // 显示扫频开始信息
                    lcd_show_string(10, 90, lcddev.width, 20, 12, "Sweep Test Started (1k-400kHz)", GREEN);
                    printf("Sweep test started by PE2 key - Optimized for speed\r\n");
                    printf("Total points: %d, Estimated time: ~30-40 seconds\r\n", SWEEP_POINTS * 2);
                } else {
                    // 停止扫频测试
                    sprintf(buttons[6].text, "SWEEP OFF");
                    buttons[6].color = GRAY;

                    // 停止定时器和ADC
                    TIM_Cmd(TIM3, DISABLE);
                    ADC_Cmd(ADC1, DISABLE);
                    ADC_Cmd(ADC2, DISABLE);

                    // 显示扫频停止信息
                    lcd_show_string(10, 90, lcddev.width, 20, 12, "Sweep Test Stopped", RED);
                    printf("Sweep test stopped by PE2 key\r\n");
                }

                // 恢复按钮正常显示
                draw_all_buttons(selected_button);

                // 延时防抖
                delay_ms(200);
            }
        }
        else
        {
            key2_pressed = 0;  // 按键释放
        }

        // 检测PA0按键（WK）- 一键启动ADC3处理（按钮八）
        if (WK == 1)  // PA0按键按下（高电平有效，因为配置为下拉）
        {
            if (wk_pressed == 0)  // 防止重复触发
            {
                wk_pressed = 1;

                // 直接启动ADC3处理（相当于按下按钮八）
                printf("PA0 Key: Starting ADC3 Processing\r\n");

                // 显示按钮按下效果
                draw_button(&buttons[7], 1, 1);
                delay_ms(100);  // 显示按下效果

                // 执行ADC3处理功能
                if (strcmp(buttons[7].text, "PROC OFF") == 0) {
                    // 启动ADC3处理
                    sprintf(buttons[7].text, "PROC ON");
                    buttons[7].color = GREEN;

                    // 重置ADC3采样状态
                    adc3_sample_index = 0;
                    adc3_sampling_complete = 0;
                    adc3_user_enabled = 1;
                    adc3_timeout_counter = 0;  // 重置超时计数器

                    // 启动ADC3采样
                    ADC_Cmd(ADC3, ENABLE);

                    // 配置102.4kHz采样率：84MHz / 821 / 1 = 102,314Hz ≈ 102.4kHz
                    TIM5_ADC3_Init(821 - 1, 1 - 1);
                    TIM_Cmd(TIM5, ENABLE);

                    // 显示ADC3处理开始信息
                    lcd_show_string(10, 90, lcddev.width, 20, 12, "ADC3 Processing Started", GREEN);
                    printf("ADC3 processing started by PA0 key\r\n");
                } else {
                    // 停止ADC3处理
                    sprintf(buttons[7].text, "PROC OFF");
                    buttons[7].color = GRAY;

                    // 停止TIM5和ADC3
                    TIM_Cmd(TIM5, DISABLE);
                    ADC_Cmd(ADC3, DISABLE);
                    adc3_user_enabled = 0;

                    // 停止DAC输出
                    DAC_StopReconstructedOutput();

                    // 显示ADC3处理停止信息
                    lcd_show_string(10, 90, lcddev.width, 20, 12, "ADC3 Processing Stopped", RED);
                    printf("ADC3 processing stopped by PA0 key\r\n");
                }

                // 恢复按钮正常显示
                draw_all_buttons(selected_button);

                // 延时防抖
                delay_ms(200);
            }
        }
        else
        {
            wk_pressed = 0;  // 按键释放
        }

        // 触屏功能已禁用，使用物理按键控制

        // 更新LCD显示（仅在频率改变时）
        if (frequency_changed) {
            // 清除频率显示区域（不影响按钮）
            lcd_fill(0, 60, lcddev.width, 120, WHITE);

            // 重新绘制按钮（先绘制按钮）
            draw_all_buttons(selected_button);

            // 格式化频率字符串
            format_frequency_display(current_frequency, lcd_buffer);

            // 计算居中位置
            uint16_t str_len = strlen(lcd_buffer);
            uint16_t x_pos = (lcddev.width - str_len * 8) / 2;
            uint16_t y_pos = 80;  // 在按钮上方显示

            // 保存当前画笔颜色
            uint32_t old_color = g_point_color;

            // 设置文字颜色并显示频率
            g_point_color = BLACK;
            lcd_show_string(x_pos, y_pos, lcddev.width, 30, 16, lcd_buffer, BLACK);

            // 恢复画笔颜色
            g_point_color = old_color;

            frequency_changed = 0;  // 清除改变标志

            // 显示DAC状态
            if (!DAC_GetUserEnable())
            {
                sprintf(lcd_buffer, "DAC: DISABLED");
                lcd_show_string(10, 270, lcddev.width, 20, 12, lcd_buffer, GRAY);
            }
            else if (current_frequency <= 100000.0f)
            {
                float multiplier = DAC_GetAmplitudeMultiplier();
                sprintf(lcd_buffer, "DAC: ON (%.1fV out)", multiplier);
                lcd_show_string(10, 270, lcddev.width, 20, 12, lcd_buffer, GREEN);
            }
            else
            {
                sprintf(lcd_buffer, "DAC: OFF (>100kHz)");
                lcd_show_string(10, 270, lcddev.width, 20, 12, lcd_buffer, RED);
            }
        }

        // 检查DAC使能状态是否改变
        if (dac_enable_changed)
        {
            dac_enable_changed = 0;  // 清除改变标志

            // 重新绘制所有按钮以更新DAC开关按钮
            draw_all_buttons(selected_button);
        }

        // 检查DAC倍数是否改变
        if (dac_multiplier_changed)
        {
            dac_multiplier_changed = 0;  // 清除改变标志

            // 重新绘制DAC倍数按钮以更新文本
            draw_all_buttons(selected_button);
        }

        // 检查ADC使能是否改变
        if (adc_enable_changed)
        {
            adc_enable_changed = 0;  // 清除改变标志

            // 重新绘制所有按钮以更新ADC开关按钮
            draw_all_buttons(selected_button);
        }

        // 处理扫频测试
        if (sweep_test_active)
        {
            // 检查当前频率点的采样是否完成
            if (adc1_sampling_complete && adc2_sampling_complete)
            {
                // 处理当前扫频点的数据
                ProcessSweepPoint();

                // 移动到下一个频率点
                current_sweep_point++;

                // 根据扫频阶段决定下一个频率点
                uint8_t continue_sweep = 0;

                if (sweep_phase == 0) {
                    // 第一阶段：只扫1kHz和1.2kHz（减少调试输出）

                    if (low_freq_points_completed < 2) {
                        float next_freq;
                        if (low_freq_points_completed == 1) {
                            next_freq = 1200.0f;  // 第二个点是1.2kHz
                            AD9833_SetFrequencyQuick1(next_freq, AD9833_OUT_SINUS1);
                            delay_ms(2);  // 频率稳定延时
                            // 优化：减少调试输出以提高扫频速度
                            // printf("DEBUG Phase 0: Set frequency to %.1f Hz\r\n", next_freq);

                            // 优化：移除实时LCD显示以提高扫频速度
                            // char progress_info[100];
                            // sprintf(progress_info, "Phase 1: Low Freq Test (%.1fkHz)", next_freq/1000.0f);
                            // lcd_show_string(10, 90, lcddev.width, 20, 12, progress_info, BLUE);

                            continue_sweep = 1;
                        } else {
                            // printf("DEBUG Phase 0: Waiting for first point to complete\r\n");
                        }
                    } else {
                        // printf("DEBUG Phase 0: Both low freq points completed\r\n");
                    }
                } else if (sweep_phase == 1) {
                    // 第二阶段：1kHz到400kHz扫频
                    if (current_sweep_point < SWEEP_POINTS) {
                        float next_freq = 1000.0f + current_sweep_point * 200.0f;
                        AD9833_SetFrequencyQuick1(next_freq, AD9833_OUT_SINUS1);
                        delay_ms(2);  // 频率稳定延时

                        // 优化：移除实时LCD显示以提高扫频速度
                        // 只在特定点显示进度（每100个点显示一次）
                        if (current_sweep_point % 100 == 0) {
                            char progress_info[100];
                            sprintf(progress_info, "Phase 2: %d/%d (%.0fkHz)",
                                    current_sweep_point, SWEEP_POINTS, next_freq/1000.0f);
                            lcd_show_string(10, 90, lcddev.width, 20, 12, progress_info, BLUE);
                        }

                        continue_sweep = 1;
                    }
                } else if (sweep_phase == 2) {
                    // 第三阶段：归一化处理（完整扫频1kHz到400kHz）
                    if (current_sweep_point < SWEEP_POINTS) {
                        float next_freq = 1000.0f + current_sweep_point * 200.0f;
                        AD9833_SetFrequencyQuick1(next_freq, AD9833_OUT_SINUS1);
                        delay_ms(2);  // 频率稳定延时

                        // 优化：移除实时LCD显示以提高扫频速度
                        // 只在特定点显示进度（每100个点显示一次）
                        if (current_sweep_point % 100 == 0) {
                            char progress_info[100];
                            sprintf(progress_info, "Phase 3: %d/%d (%.0fkHz)",
                                    current_sweep_point, SWEEP_POINTS, next_freq/1000.0f);
                            lcd_show_string(10, 90, lcddev.width, 20, 12, progress_info, BLUE);
                        }

                        continue_sweep = 1;
                    }
                }

                if (continue_sweep) {
                    // 重新启动ADC采样
                    ADC1_ResetSampling();
                    ADC2_ResetSampling();
                    ADC1_StartSampling();
                    ADC2_StartSampling();
                }
                else
                {
                    if (sweep_phase == 0) {
                        // 第一阶段完成，判断倍数并开始第二阶段
                        if (first_sweep_1kHz_ratio > 0.0f && first_sweep_1_2kHz_ratio > 0.0f) {
                            printf("DEBUG: 1kHz=%.6f, 1.2kHz=%.6f\r\n", first_sweep_1kHz_ratio, first_sweep_1_2kHz_ratio);
                            if (first_sweep_1kHz_ratio < 0.7f && first_sweep_1_2kHz_ratio < 0.7f) {
                                amplitude_multiplier = 2;
                                printf("Low frequency ratios detected, applying 2x multiplier\r\n");
                            } else {
                                amplitude_multiplier = 1;
                                printf("Normal frequency ratios, using 1x multiplier\r\n");
                            }
                        } else {
                            amplitude_multiplier = 1;
                            printf("Insufficient low frequency data, using 1x multiplier\r\n");
                            printf("DEBUG: 1kHz=%.6f, 1.2kHz=%.6f\r\n", first_sweep_1kHz_ratio, first_sweep_1_2kHz_ratio);
                        }
                        printf("DEBUG: Final amplitude_multiplier = %d\r\n", amplitude_multiplier);

                        sweep_phase = 1;
                        current_sweep_point = 0;
                        max_voltage_ratio = 0.0f;  // 重置最大值

                        // 显示阶段切换信息
                        lcd_show_string(10, 90, lcddev.width, 20, 12, "Phase 2: Main Sweep Starting", BLUE);

                        // 输出第二次扫频的数据格式说明
                        printf("=== PHASE 2: 1kHz-400kHz FREQUENCY RESPONSE ===\r\n");
                        printf("Frequency(Hz)\tVoltage_Ratio\r\n");

                        // 设置起始频率重新开始扫频
                        float start_freq = 1000.0f;
                        AD9833_SetFrequencyQuick1(start_freq, AD9833_OUT_SINUS1);
                        delay_ms(5);  // 优化：减少延时

                        // 重新启动ADC采样
                        ADC1_ResetSampling();
                        ADC2_ResetSampling();
                        ADC1_StartSampling();
                        ADC2_StartSampling();

                        lcd_show_string(10, 90, lcddev.width, 20, 12, "Phase 2: 1k-400k Sweep", BLUE);
                    } else if (sweep_phase == 1) {
                        // 第二阶段完成，输出结束标记
                        printf("=== END OF PHASE 2: 1kHz-400kHz FREQUENCY RESPONSE ===\r\n");

                        // 标记第二次扫频完成，并将数据加载到校正数组
                        sweep_phase2_completed = true;
                        LoadSweepPhase2ToCorrection();
                        printf("Phase 2 sweep data saved (%d points) for button 8 correction\r\n", sweep_phase2_count);

                        // 显示一些关键频率点的校正数据
                        printf("Key correction points:\r\n");
                        printf("  1kHz: %.3f\r\n", GetSweepPhase2Ratio(1000.0f));
                        printf("  5kHz: %.3f\r\n", GetSweepPhase2Ratio(5000.0f));
                        printf("  10kHz: %.3f\r\n", GetSweepPhase2Ratio(10000.0f));
                        printf("  20kHz: %.3f\r\n", GetSweepPhase2Ratio(20000.0f));
                        printf("  50kHz: %.3f\r\n", GetSweepPhase2Ratio(50000.0f));
                        printf("  100kHz: %.3f\r\n", GetSweepPhase2Ratio(100000.0f));

                        // 开始第三阶段
                        sweep_phase = 2;
                        current_sweep_point = 0;

                        // 显示阶段切换信息
                        lcd_show_string(10, 90, lcddev.width, 20, 12, "Phase 3: Normalize Starting", BLUE);

                        // 设置起始频率重新开始扫频
                        float start_freq = 1000.0f;
                        AD9833_SetFrequencyQuick1(start_freq, AD9833_OUT_SINUS1);
                        delay_ms(5);  // 优化：减少延时

                        // 重新启动ADC采样
                        ADC1_ResetSampling();
                        ADC2_ResetSampling();
                        ADC1_StartSampling();
                        ADC2_StartSampling();
                    } else {
                        // 第三阶段完成，扫频测试结束
                        StopSweepTest();
                        OutputSweepResults();

                        // 判断并显示滤波器类型
                        DetermineFilterType();

                        lcd_show_string(10, 90, lcddev.width, 20, 12, "Sweep Test Complete! (Optimized)", GREEN);
                        printf("=== SWEEP TEST COMPLETED - PERFORMANCE OPTIMIZED ===\r\n");
                    }
                }
            }
        }

        // 检查ADC1采样是否完成（非扫频模式）
        if (adc1_sampling_complete && !sweep_test_active && adc_user_enabled)
        {
            // 显示采样完成信息
            char sample_info[100];
            sprintf(sample_info, "ADC1: 4096 samples complete");
            lcd_show_string(10, 90, lcddev.width, 20, 12, sample_info, BLUE);

            // 显示一些采样数据（前几个点）
            sprintf(sample_info, "Data[0-3]: %d %d %d %d",
                    adc1_sample_buffer[0], adc1_sample_buffer[1],
                    adc1_sample_buffer[2], adc1_sample_buffer[3]);
            lcd_show_string(10, 110, lcddev.width, 20, 12, sample_info, GREEN);

            // 通过串口输出所有ADC1采样数据
            printf("ADC1_SAMPLES_START\r\n");
            for (int i = 0; i < ADC1_SAMPLE_SIZE; i++)
            {
                printf("%d\t", adc1_sample_buffer[i]);
            }
            printf("ADC1_SAMPLES_END\r\n");

            // 重置采样状态，准备下次采样
            adc1_sampling_complete = 0;
            adc1_sample_index = 0;
        }

        // ADC3超时检查（简化版本）
        if (adc3_user_enabled && !adc3_sampling_complete)
        {
            adc3_timeout_counter++;
            if (adc3_timeout_counter > 50000)  // 减少超时时间，避免长时间卡死
            {
                printf("ADC3: Timeout! Stopping...\r\n");

                // 超时处理：强制停止
                TIM_Cmd(TIM5, DISABLE);
                ADC_Cmd(ADC3, DISABLE);
                adc3_user_enabled = 0;
                adc3_timeout_counter = 0;

                // 更新按钮状态
                sprintf(buttons[7].text, "PROC OFF");
                buttons[7].color = GRAY;
                draw_button(&buttons[7], 0, 0);

                // 显示超时信息
                lcd_show_string(10, 90, lcddev.width, 20, 12, "ADC3 Timeout!", RED);
            }
        }

        // 处理ADC3 FFT分析（只进行一次）
        if (adc3_user_enabled && adc3_sampling_complete)
        {
            printf("ADC3: Starting processing, samples=%d\r\n", adc3_sample_index);

            // 停止TIM5和ADC3，防止继续采样
            TIM_Cmd(TIM5, DISABLE);
            ADC_Cmd(ADC3, DISABLE);

            // 检查采样数据是否有效
            if (adc3_sample_index >= 512)
            {
                printf("ADC3: Valid samples, starting FFT...\r\n");
                ADC3_ProcessFFT();
                printf("ADC3: FFT processing completed\r\n");
            }
            else
            {
                printf("ADC3: Invalid sample count: %d\r\n", adc3_sample_index);
            }

            // 标记处理完成，不再重新采样
            adc3_user_enabled = 0;
            adc3_timeout_counter = 0;

            // 更新按钮状态
            sprintf(buttons[7].text, "PROC OFF");
            buttons[7].color = GRAY;
            draw_button(&buttons[7], 0, 0);

            // 更新LCD显示
            char debug_buffer[100];
            sprintf(debug_buffer, "ADC3: Complete (%d samples)", adc3_sample_index);
            lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, BLUE);
        }



        delay_ms(5);  // 主循环延时 - 优化：减少延时提高扫频速度
    }
}

// ADC1采样控制函数实现
void ADC1_StartSampling(void)
{
    // 重置采样状态
    adc1_sample_index = 0;
    adc1_sampling_complete = 0;

    // 清空缓冲区（可选）
    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        adc1_sample_buffer[i] = 0;
    }

    // 启动ADC1
    ADC_Cmd(ADC1, ENABLE);

    // 如果使用定时器触发，确保定时器运行
    TIM_Cmd(TIM3, ENABLE);
}

void ADC1_StopSampling(void)
{
    // 停止ADC1
    ADC_Cmd(ADC1, DISABLE);

    // 可以选择停止定时器（如果不影响其他功能）
    // TIM_Cmd(TIM3, DISABLE);
}

void ADC1_ResetSampling(void)
{
    // 重置采样状态
    adc1_sample_index = 0;
    adc1_sampling_complete = 0;

    // 清空缓冲区
    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        adc1_sample_buffer[i] = 0;
    }
}

// ADC2采样控制函数实现
void ADC2_StartSampling(void)
{
    // 重置采样状态
    adc2_sample_index = 0;
    adc2_sampling_complete = 0;

    // 清空缓冲区（可选）
    for (int i = 0; i < ADC2_SAMPLE_SIZE; i++) {
        adc2_sample_buffer[i] = 0;
    }

    // 启动ADC2
    ADC_Cmd(ADC2, ENABLE);

    // 如果使用定时器触发，确保定时器运行
    TIM_Cmd(TIM3, ENABLE);
}

void ADC2_StopSampling(void)
{
    // 停止ADC2
    ADC_Cmd(ADC2, DISABLE);

    // 可以选择停止定时器（如果不影响其他功能）
    // TIM_Cmd(TIM3, DISABLE);
}

void ADC2_ResetSampling(void)
{
    // 重置采样状态
    adc2_sample_index = 0;
    adc2_sampling_complete = 0;

    // 清空缓冲区
    for (int i = 0; i < ADC2_SAMPLE_SIZE; i++) {
        adc2_sample_buffer[i] = 0;
    }
}

// ADC3采样控制函数实现
void ADC3_StartSampling(void)
{
    // 重置采样状态
    adc3_sample_index = 0;
    adc3_sampling_complete = 0;

    // 清空缓冲区（可选）
    for (int i = 0; i < ADC3_SAMPLE_SIZE; i++) {
        adc3_sample_buffer[i] = 0;
    }



    // 启动ADC3
    ADC_Cmd(ADC3, ENABLE);

    // 如果使用定时器触发，确保TIM5运行
    TIM_Cmd(TIM5, ENABLE);


}

void ADC3_StopSampling(void)
{
    // 停止ADC3
    ADC_Cmd(ADC3, DISABLE);

    // 可以选择停止定时器（如果不影响其他功能）
    // TIM_Cmd(TIM3, DISABLE);
}

void ADC3_ResetSampling(void)
{
    // 重置采样状态
    adc3_sample_index = 0;
    adc3_sampling_complete = 0;

    // 清空缓冲区
    for (int i = 0; i < ADC3_SAMPLE_SIZE; i++) {
        adc3_sample_buffer[i] = 0;
    }
}

// 扫频测试函数实现
void StartSweepTest(void)
{
    sweep_test_active = 1;
    current_sweep_point = 0;
    total_sweep_points = 0;
    sweep_sampling_complete = 0;
    max_voltage_ratio = 0.0f;
    sweep_phase = 0;  // 第一阶段：低频检测

    // 清空第二次扫频数据，准备新的扫频
    ClearSweepPhase2Data();

    // 初始化滤波器类型判断相关变量
    freq_1kHz_ratio = 0.0f;
    freq_1_2kHz_ratio = 0.0f;
    freq_399_8kHz_ratio = 0.0f;
    freq_400kHz_ratio = 0.0f;

    // 初始化第一次扫频的低频检测变量
    first_sweep_1kHz_ratio = 0.0f;
    first_sweep_1_2kHz_ratio = 0.0f;
    amplitude_multiplier = 1;
    low_freq_points_completed = 0;

    // 初始化平滑滤波器
    InitSmoothFilters();

    // 清空结果缓冲区
    for (int i = 0; i < SWEEP_BUFFER_SIZE; i++) {
        sweep_results[i].frequency = 0;
        sweep_results[i].adc1_amplitude = 0;
        sweep_results[i].adc2_amplitude = 0;
        sweep_results[i].voltage_ratio = 0;
    }

    // 设置起始频率 1kHz
    float start_freq = 1000.0f;
    AD9833_SetFrequencyQuick1(start_freq, AD9833_OUT_SINUS1);
    printf("DEBUG StartSweepTest: Set initial frequency to %.1f Hz\r\n", start_freq);

    // 开始扫频测试，无串口输出

    // 等待频率稳定（优化：减少延时）
    delay_ms(5);

    // 启动ADC1和ADC2同步采样
    ADC1_ResetSampling();
    ADC2_ResetSampling();
    ADC1_StartSampling();
    ADC2_StartSampling();
}

void StopSweepTest(void)
{
    sweep_test_active = 0;

    // 停止ADC采样
    ADC1_StopSampling();
    ADC2_StopSampling();
}

void ProcessSweepPoint(void)
{
    float current_freq = 1000.0f + current_sweep_point * 200.0f;

    // 优化：减少调试输出以提高扫频速度
    // 只在第一个点输出调试信息
    if (current_sweep_point == 0) {
        printf("DEBUG ProcessSweepPoint: phase=%d, point=%d, freq=%.1f, multiplier=%d\r\n",
               sweep_phase, current_sweep_point, current_freq, amplitude_multiplier);
    }

    // 计算ADC1和ADC2的RMS值（去除直流分量）
    float adc1_dc = 0, adc2_dc = 0;
    float adc1_rms = 0, adc2_rms = 0;

    // 根据是否在扫频测试中决定实际使用的采样点数
    uint16_t actual_samples = sweep_test_active ? 64 : ADC1_SAMPLE_SIZE;

    // 计算直流分量（只使用实际采样的点数）
    for (int i = 0; i < actual_samples; i++) {
        adc1_dc += adc1_sample_buffer[i];
        adc2_dc += adc2_sample_buffer[i];
    }
    adc1_dc /= actual_samples;
    adc2_dc /= actual_samples;

    // 计算RMS值（去除直流分量，只使用实际采样的点数）
    for (int i = 0; i < actual_samples; i++) {
        float adc1_ac = adc1_sample_buffer[i] - adc1_dc;
        float adc2_ac = adc2_sample_buffer[i] - adc2_dc;
        adc1_rms += adc1_ac * adc1_ac;
        adc2_rms += adc2_ac * adc2_ac;
    }
    adc1_rms = sqrtf(adc1_rms / actual_samples);
    adc2_rms = sqrtf(adc2_rms / actual_samples);





    // 计算电压幅度比（滤波器输出/输入，线性比值，不是dB）
    float voltage_ratio = 0.0f;
    if (adc1_rms > 0) {
        voltage_ratio = adc2_rms / adc1_rms;
    }

    if (sweep_phase == 0) {
        // 第一阶段：只检测1kHz和1.2kHz（减少调试输出）
        if (fabs(current_freq - 1000.0f) < 1.0f) {          // 1kHz ± 1Hz
            first_sweep_1kHz_ratio = voltage_ratio;
            low_freq_points_completed++;
            printf("1kHz ratio: %.6f\r\n", voltage_ratio);
        } else if (fabs(current_freq - 1200.0f) < 1.0f) {   // 1.2kHz ± 1Hz
            first_sweep_1_2kHz_ratio = voltage_ratio;
            low_freq_points_completed++;
            printf("1.2kHz ratio: %.6f\r\n", voltage_ratio);
        }
    } else if (sweep_phase == 1) {
        // 第二阶段：1kHz到400kHz扫频，输出电压幅度比
        float adjusted_voltage_ratio = voltage_ratio * amplitude_multiplier;

        // 应用平滑滤波到幅度比
        float smoothed_ratio = ApplySmoothFilter(adjusted_voltage_ratio,
                                               smooth_buffer_phase2,
                                               &smooth_index_phase2,
                                               &smooth_count_phase2,
                                               SMOOTH_FILTER_SIZE);

        // 保存第二次扫频数据（用于第八个按钮功能）
        SaveSweepPhase2Data(current_freq, smoothed_ratio);

        // 输出：频率(Hz) \t 电压幅度比（减少输出频率）
        if (current_sweep_point % 10 == 0) {  // 每10个点输出一次，减少串口负担
            printf("%.1f\t%.6f\r\n", current_freq, smoothed_ratio);
        }

        // 寻找最大值（使用平滑后的值）
        if (smoothed_ratio > max_voltage_ratio) {
            max_voltage_ratio = smoothed_ratio;
        }
    } else {
        // 第三阶段：归一化处理，应用平滑滤波，不输出串口数据
        float normalized_ratio = 0.0f;
        float adjusted_voltage_ratio = voltage_ratio * amplitude_multiplier;

        if (max_voltage_ratio > 0.0f) {
            normalized_ratio = adjusted_voltage_ratio / max_voltage_ratio;
        }

        // 应用平滑滤波到归一化幅度比
        float smoothed_normalized_ratio = ApplySmoothFilter(normalized_ratio,
                                                          smooth_buffer_phase3,
                                                          &smooth_index_phase3,
                                                          &smooth_count_phase3,
                                                          SMOOTH_FILTER_SIZE);

        // 存储特定频率点的平滑后归一化电压幅度比用于滤波器类型判断
        if (fabs(current_freq - 1000.0f) < 1.0f) {          // 1kHz ± 1Hz
            freq_1kHz_ratio = smoothed_normalized_ratio;
        } else if (fabs(current_freq - 1200.0f) < 1.0f) {   // 1.2kHz ± 1Hz
            freq_1_2kHz_ratio = smoothed_normalized_ratio;
        } else if (fabs(current_freq - 399800.0f) < 1.0f) { // 399.8kHz ± 1Hz
            freq_399_8kHz_ratio = smoothed_normalized_ratio;
        } else if (fabs(current_freq - 400000.0f) < 1.0f) { // 400kHz ± 1Hz
            freq_400kHz_ratio = smoothed_normalized_ratio;
        }
    }

    // 只在缓冲区中保存最近的数据用于分析
    int buffer_idx = current_sweep_point % SWEEP_BUFFER_SIZE;
    sweep_results[buffer_idx].frequency = current_freq;
    sweep_results[buffer_idx].adc1_amplitude = adc1_rms;
    sweep_results[buffer_idx].adc2_amplitude = adc2_rms;
    sweep_results[buffer_idx].voltage_ratio = voltage_ratio;


    total_sweep_points++;
}

void OutputSweepResults(void)
{
    // 扫频完成，无额外输出
}



void DetermineFilterType(void)
{
    // 判断滤波器类型的标准：
    // 1、低通滤波器：1kHz和1.2kHz的归一化电压幅度比均大于0.7且399.8kHz和400kHz的归一化电压幅度比均小于0.7
    // 2、高通滤波器：1kHz和1.2kHz的归一化电压幅度比均小于0.7且399.8kHz和400kHz的归一化电压幅度比均大于0.7
    // 3、带通滤波器：1kHz和1.2kHz的归一化电压幅度比均小于0.7且399.8kHz和400kHz的归一化电压幅度比均小于0.7
    // 4、带阻滤波器：1kHz和1.2kHz的归一化电压幅度比均大于0.7且399.8kHz和400kHz的归一化电压幅度比均大于0.7

    char filter_type[50] = "Unknown";
    uint16_t display_color = RED;

    // 检查是否有足够的数据进行判断
//    if (freq_1kHz_ratio == 0.0f || freq_1_2kHz_ratio == 0.0f ||
//        freq_399_8kHz_ratio == 0.0f || freq_400kHz_ratio == 0.0f) {
//        sprintf(filter_type, "Insufficient Data");
//        display_color = GRAY;
//    } else 
{
        // 判断低频段（1kHz和1.2kHz）
        uint8_t low_freq_high = (freq_1kHz_ratio > 0.7f) && (freq_1_2kHz_ratio > 0.7f);

        // 判断高频段（399.8kHz和400kHz）
        uint8_t high_freq_high = (freq_399_8kHz_ratio > 0.7f) && (freq_400kHz_ratio > 0.7f);

        if (low_freq_high && !high_freq_high) {
            // 低频高，高频低 -> 低通滤波器
            sprintf(filter_type, "Low-Pass Filter");
            display_color = GREEN;
        } else if (!low_freq_high && high_freq_high) {
            // 低频低，高频高 -> 高通滤波器
            sprintf(filter_type, "High-Pass Filter");
            display_color = BLUE;
        } else if (!low_freq_high && !high_freq_high) {
            // 低频低，高频低 -> 带通滤波器
            sprintf(filter_type, "Band-Pass Filter");
            display_color = MAGENTA;
        } else if (low_freq_high && high_freq_high) {
            // 低频高，高频高 -> 带阻滤波器
            sprintf(filter_type, "Band-Stop Filter");
            display_color = YELLOW;
        }
    }

    // 在LCD屏幕上显示滤波器类型
    // 清除之前的显示区域
    lcd_fill(0, 110, lcddev.width, 150, WHITE);

    // 显示滤波器类型
    lcd_show_string(10, 110, lcddev.width, 20, 16, "Filter Type:", BLACK);
    lcd_show_string(10, 130, lcddev.width, 20, 16, filter_type, display_color);

    // 显示具体的测量值（调试信息）
    char debug_info[100];
    sprintf(debug_info, "1kHz:%.3f 1.2k:%.3f", freq_1kHz_ratio, freq_1_2kHz_ratio);
    lcd_show_string(10, 150, lcddev.width, 20, 12, debug_info, BLACK);

    sprintf(debug_info, "399.8k:%.3f 400k:%.3f", freq_399_8kHz_ratio, freq_400kHz_ratio);
    lcd_show_string(10, 170, lcddev.width, 20, 12, debug_info, BLACK);

    // 通过串口输出结果
    printf("=== FILTER TYPE DETERMINATION ===\r\n");
    printf("Filter Type: %s\r\n", filter_type);
    printf("Amplitude Multiplier Used: %dx\r\n", amplitude_multiplier);
    printf("First Sweep Low Freq Data:\r\n");
    printf("  1kHz (raw): %.6f\r\n", first_sweep_1kHz_ratio);
    printf("  1.2kHz (raw): %.6f\r\n", first_sweep_1_2kHz_ratio);
    printf("Final Normalized Data:\r\n");
    printf("  1kHz: %.6f\r\n", freq_1kHz_ratio);
    printf("  1.2kHz: %.6f\r\n", freq_1_2kHz_ratio);
    printf("  399.8kHz: %.6f\r\n", freq_399_8kHz_ratio);
    printf("  400kHz: %.6f\r\n", freq_400kHz_ratio);
    printf("=== END OF DETERMINATION ===\r\n");


}

/**
 * @brief  初始化平滑滤波器
 * @param  None
 * @retval None
 * @note   清空所有平滑滤波器的缓冲区和计数器
 */
void InitSmoothFilters(void)
{
    // 清空第二次扫频平滑缓冲区
    for (int i = 0; i < SMOOTH_FILTER_SIZE; i++) {
        smooth_buffer_phase2[i] = 0.0f;
        smooth_buffer_phase3[i] = 0.0f;
    }

    // 重置索引和计数器
    smooth_index_phase2 = 0;
    smooth_index_phase3 = 0;
    smooth_count_phase2 = 0;
    smooth_count_phase3 = 0;
}

/**
 * @brief  应用移动平均平滑滤波器
 * @param  new_value: 新的输入值
 * @param  buffer: 滤波器缓冲区指针
 * @param  index: 缓冲区索引指针
 * @param  count: 有效数据计数指针
 * @param  buffer_size: 缓冲区大小
 * @retval 平滑后的输出值
 * @note   使用移动平均算法对输入数据进行平滑处理
 */
float ApplySmoothFilter(float new_value, float* buffer, uint8_t* index, uint8_t* count, uint8_t buffer_size)
{
    // 将新值存入缓冲区
    buffer[*index] = new_value;

    // 更新索引（循环缓冲区）
    *index = (*index + 1) % buffer_size;

    // 更新有效数据计数（最多为缓冲区大小）
    if (*count < buffer_size) {
        (*count)++;
    }

    // 计算移动平均值
    float sum = 0.0f;
    for (int i = 0; i < *count; i++) {
        sum += buffer[i];
    }

    return sum / (*count);
}















/**
 * @brief  从扫频结果中获取指定频率的幅度响应
 * @param  frequency: 查询频率 (Hz)
 * @retval 幅度响应（线性比值，基于第二次扫频的结果）
 * @note   使用线性插值计算中间频率的响应，超出范围返回0
 */
float GetAmplitudeResponseFromSweep(float frequency)
{
    // 检查是否有有效的扫频数据
    if (total_sweep_points < 2) {
        printf("Warning: No sweep data available for freq %.0f Hz\r\n", frequency);
        return 0.0f;
    }

    // 超出扫频范围的频率返回0（十次谐波频率在范围之外的幅度响应记0）
    if (frequency < 1000.0f || frequency > 100000.0f) {
        printf("Freq %.0f Hz out of sweep range (1k-100k Hz), response = 0\r\n", frequency);
        return 0.0f;
    }

    int valid_points = (total_sweep_points < SWEEP_BUFFER_SIZE) ? total_sweep_points : SWEEP_BUFFER_SIZE;

    // 查找最接近的频率点
    float closest_freq_diff = 1000000.0f;
    int closest_idx = -1;
    float exact_response = 0.0f;

    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0) {  // 有效数据
            float freq_diff = fabsf(sweep_results[i].frequency - frequency);

            // 如果找到完全匹配的频率
            if (freq_diff < 1.0f) {  // 1Hz误差内认为匹配
                exact_response = sweep_results[i].adc2_amplitude;
                printf("Exact match: %.0f Hz -> response %.6f\r\n", frequency, exact_response);
                return exact_response;
            }

            if (freq_diff < closest_freq_diff) {
                closest_freq_diff = freq_diff;
                closest_idx = i;
            }
        }
    }

    // 如果没有找到足够接近的点，返回最接近点的响应
    if (closest_idx >= 0) {
        float response = sweep_results[closest_idx].adc2_amplitude;
        printf("Closest match: %.0f Hz -> %.0f Hz, response %.6f\r\n",
               frequency, sweep_results[closest_idx].frequency, response);
        return response;
    }

    printf("Warning: No valid sweep data found for freq %.0f Hz\r\n", frequency);
    return 0.0f;
}

// ADC3 FFT处理函数实现
void ADC3_ProcessFFT(void)
{
    printf("ADC3_ProcessFFT: Starting, samples=%d\r\n", ADC3_SAMPLE_SIZE);

    // 安全检查：确保有足够的采样数据
    if (adc3_sample_index < ADC3_SAMPLE_SIZE) {
        printf("ADC3_ProcessFFT: Error - insufficient samples: %d\r\n", adc3_sample_index);
        return;
    }

    // 步骤1：计算ADC3的直流分量
    float adc3_dc = 0;
    for (int i = 0; i < ADC3_SAMPLE_SIZE; i++) {
        adc3_dc += adc3_sample_buffer[i];
    }
    adc3_dc /= ADC3_SAMPLE_SIZE;

    printf("ADC3_ProcessFFT: DC component: %.2f\r\n", adc3_dc);

    // 步骤2：滤掉直流分量，保存到临时缓冲区
    for (int i = 0; i < ADC3_SAMPLE_SIZE; i++) {
        adc3_dc_removed[i] = (float)adc3_sample_buffer[i] - adc3_dc;
    }

    // 步骤3：将去直流后的数据转换为FFT输入格式
    for (int i = 0; i < ADC3_SAMPLE_SIZE; i++) {
        adc3_fft_inputbuf[2*i] = adc3_dc_removed[i] * (3.3f / 4096.0f);  // 转换为电压
        adc3_fft_inputbuf[2*i+1] = 0.0f;  // 虚部为0
    }



    // 暂时跳过窗函数处理，简化计算
    printf("ADC3_ProcessFFT: Skipping window function for debugging\r\n");
    float window_gain = 1.0f;  // 设置为1，表示无窗函数


    // 使用512点FFT
    printf("ADC3_ProcessFFT: Starting 512-point FFT...\r\n");
    printf("ADC3_ProcessFFT: Input buffer ready, first few values: %.2f, %.2f, %.2f\r\n",
           adc3_fft_inputbuf[0], adc3_fft_inputbuf[2], adc3_fft_inputbuf[4]);

    arm_cfft_f32(&arm_cfft_sR_f32_len512, adc3_fft_inputbuf, 0, 1);
    printf("ADC3_ProcessFFT: FFT completed\r\n");

    // 计算幅度谱（512点）
    printf("ADC3_ProcessFFT: Calculating magnitude spectrum...\r\n");
    arm_cmplx_mag_f32(adc3_fft_inputbuf, adc3_fft_outputbuf, ADC3_SAMPLE_SIZE);
    printf("ADC3_ProcessFFT: Magnitude spectrum calculated\r\n");

    // 应用窗函数增益补偿
    for (int i = 0; i < ADC3_SAMPLE_SIZE; i++) {
        adc3_fft_outputbuf[i] /= window_gain;
    }



    // 频率分辨率：102.314kHz / 512 ≈ 199.8Hz
    float freq_resolution = 102314.0f / ADC3_SAMPLE_SIZE;

    // 寻找基波频率（最大幅度，用于DAC输出优化）
    float max_amplitude = 0.0f;
    int fundamental_bin = 0;

    // 在所有频率范围内寻找最大幅度，跳过直流分量
    for (int i = 1; i < ADC3_SAMPLE_SIZE/2; i++) {
        if (adc3_fft_outputbuf[i] > max_amplitude) {
            max_amplitude = adc3_fft_outputbuf[i];
            fundamental_bin = i;
        }
    }

    // 计算基波频率
    float fundamental_freq = fundamental_bin * freq_resolution;

    // 输出ADC3 FFT分析结果 - 基波及有效谐波
    printf("\r\n=== ADC3 FFT Analysis Results ===\r\n");
    printf("Sampling Rate: 102.314 kHz, FFT Points: %d\r\n", ADC3_SAMPLE_SIZE);
    printf("Frequency Resolution: %.2f Hz\r\n", freq_resolution);
    printf("DC Component: %.3f V\r\n", adc3_dc * (3.3f / 4096.0f));
    printf("\r\n--- Fundamental and Harmonics ---\r\n");
    printf("Order\tFrequency(Hz)\tAmplitude(V)\tPhase(deg)\r\n");

    // 输出基波信息
    float fundamental_voltage = max_amplitude * (3.3f / 4096.0f);  // 转换为电压值
    float fundamental_phase = atan2(adc3_fft_inputbuf[2*fundamental_bin+1],
                                   adc3_fft_inputbuf[2*fundamental_bin]) * 180.0f / M_PI;
    printf("1\t%.1f\t\t%.6f\t\t%.1f\r\n", fundamental_freq, fundamental_voltage, fundamental_phase);

    // 设置幅度阈值，只输出显著的谐波
    float amplitude_threshold = max_amplitude * 0.05f;  // 最大幅度的5%作为阈值
    float voltage_threshold = amplitude_threshold * (3.3f / 4096.0f);

    // 搜索谐波（2次到20次谐波）
    int harmonic_count = 0;
    float nyquist_freq = 102314.0f / 2.0f;  // 奈奎斯特频率

    for (int harmonic = 2; harmonic <= 20 && harmonic_count < 15; harmonic++) {
        float expected_harmonic_freq = fundamental_freq * harmonic;

        // 检查谐波是否超出奈奎斯特频率
        if (expected_harmonic_freq > nyquist_freq) {
            break;
        }

        int harmonic_bin = fundamental_bin * harmonic;
        if (harmonic_bin >= ADC3_SAMPLE_SIZE/2) {
            break;
        }

        // 在谐波附近搜索真实峰值（±2个bin）
        int search_start = harmonic_bin - 2;
        int search_end = harmonic_bin + 2;
        if (search_start < 1) search_start = 1;
        if (search_end >= ADC3_SAMPLE_SIZE/2) search_end = ADC3_SAMPLE_SIZE/2 - 1;

        float peak_amplitude = 0.0f;
        int peak_bin = harmonic_bin;
        for (int j = search_start; j <= search_end; j++) {
            if (adc3_fft_outputbuf[j] > peak_amplitude) {
                peak_amplitude = adc3_fft_outputbuf[j];
                peak_bin = j;
            }
        }

        // 验证是否为真正的谐波（频率误差在±5%以内）
        float actual_freq = peak_bin * freq_resolution;
        float freq_error = fabs(actual_freq - expected_harmonic_freq) / expected_harmonic_freq;

        // 只输出显著且频率正确的谐波
        if (peak_amplitude > amplitude_threshold && freq_error < 0.05f) {
            float harmonic_voltage = peak_amplitude * (3.3f / 4096.0f);
            float harmonic_phase = atan2(adc3_fft_inputbuf[2*peak_bin+1],
                                        adc3_fft_inputbuf[2*peak_bin]) * 180.0f / M_PI;

            printf("%d\t%.1f\t\t%.6f\t\t%.1f\r\n", harmonic, actual_freq, harmonic_voltage, harmonic_phase);
            harmonic_count++;
        }
    }

    printf("\r\nTotal Harmonics Found: %d (above %.6f V threshold)\r\n", harmonic_count, voltage_threshold);
    printf("=== End of ADC3 FFT Analysis ===\r\n\r\n");

    // 调用专门的FFT结果输出函数，提供更详细的分析
    ADC3_OutputFFTResults();

    // 应用扫频校正（如果启用且有数据）
    if (use_sweep_correction && sweep_correction_count > 0) {
        printf("ADC3_ProcessFFT: Applying sweep correction to FFT results...\r\n");
        ADC3_ApplySweepCorrection();
        printf("ADC3_ProcessFFT: Sweep correction applied to %d frequency bins\r\n", sweep_correction_count);
    } else {
        printf("ADC3_ProcessFFT: No sweep correction applied\r\n");
    }

    // 根据基波频率计算DAC输出参数
    ADC3_CalculateOptimalDACParams(fundamental_freq);

    // 智能重构：根据谐波复杂度选择输出方式
    ADC3_IntelligentReconstruction();

    printf("ADC3_ProcessFFT: All processing completed\r\n");

    // 注意：扫频校正数据需要在第一次处理前加载
    // 可以通过串口命令或其他方式触发 ADC3_LoadSweepResults();

    // 在LCD上显示FFT信息
    char lcd_info[100];
    sprintf(lcd_info, "FFT: 512pts@102.4kHz");
    lcd_show_string(10, 90, lcddev.width, 20, 12, lcd_info, BLUE);
}

// 根据基波频率计算最佳DAC输出参数（限制采样率不超过512kHz）
void ADC3_CalculateOptimalDACParams(float fundamental_freq)
{
    adc3_fundamental_freq = fundamental_freq;

    // DAC采样率上限512kHz
    const float MAX_DAC_SAMPLE_RATE = 512000.0f;

    // 限制频率范围在1kHz到50kHz之间
    if (fundamental_freq < 1000.0f) fundamental_freq = 1000.0f;
    if (fundamental_freq > 50000.0f) fundamental_freq = 50000.0f;

    // 根据频率范围（1kHz-50kHz）和512kHz采样率上限优化点数配置
    if (fundamental_freq <= 2000.0f) {
        // 1kHz-2kHz：使用256点，采样率256kHz-512kHz，最佳波形质量
        adc3_points_per_cycle = 256;
    } else if (fundamental_freq <= 4000.0f) {
        // 2kHz-4kHz：使用128点，采样率256kHz-512kHz，优秀波形质量
        adc3_points_per_cycle = 128;
    } else if (fundamental_freq <= 8000.0f) {
        // 4kHz-8kHz：使用64点，采样率256kHz-512kHz，良好波形质量
        adc3_points_per_cycle = 64;
    } else if (fundamental_freq <= 16000.0f) {
        // 8kHz-16kHz：使用32点，采样率256kHz-512kHz，可接受波形质量
        adc3_points_per_cycle = 32;
    } else if (fundamental_freq <= 32000.0f) {
        // 16kHz-32kHz：使用16点，采样率256kHz-512kHz，基本波形质量
        adc3_points_per_cycle = 16;
    } else {
        // 32kHz-50kHz：使用动态计算点数，保持512kHz采样率
        adc3_points_per_cycle = (uint16_t)(512000.0f / fundamental_freq);
        if (adc3_points_per_cycle < 8) adc3_points_per_cycle = 8;
    }

    // 计算理论DAC采样率
    float cycle_time = 1.0f / fundamental_freq;
    float theoretical_sample_rate = (float)adc3_points_per_cycle / cycle_time;

    // 限制采样率不超过512kHz
    if (theoretical_sample_rate > MAX_DAC_SAMPLE_RATE) {
        // 重新计算点数以满足512kHz上限
        adc3_points_per_cycle = (uint16_t)(MAX_DAC_SAMPLE_RATE * cycle_time);

        // 确保最少8点，最多256点
        if (adc3_points_per_cycle < 8) adc3_points_per_cycle = 8;
        if (adc3_points_per_cycle > 256) adc3_points_per_cycle = 256;

        // 重新计算实际采样率
        adc3_dac_sample_rate = (float)adc3_points_per_cycle / cycle_time;
    } else {
        adc3_dac_sample_rate = theoretical_sample_rate;
    }

    // 确保采样率在安全范围内
    if (adc3_dac_sample_rate > MAX_DAC_SAMPLE_RATE) {
        adc3_dac_sample_rate = MAX_DAC_SAMPLE_RATE;
        adc3_points_per_cycle = (uint16_t)(adc3_dac_sample_rate / fundamental_freq);
        if (adc3_points_per_cycle < 8) adc3_points_per_cycle = 8;
    }

    // 重新计算精确的采样率和输出频率
    adc3_dac_sample_rate = fundamental_freq * (float)adc3_points_per_cycle;
    float actual_output_freq = adc3_dac_sample_rate / (float)adc3_points_per_cycle;

    printf("ADC3 DAC Params: freq=%.1fHz, points=%d, sample_rate=%.0fHz (output=%.1fHz)\r\n",
           fundamental_freq, adc3_points_per_cycle, adc3_dac_sample_rate, actual_output_freq);
}

// ADC3反FFT重构信号函数（简化版本）
void ADC3_ReconstructSignal_Simplified(void)
{
    printf("ADC3_ReconstructSignal_Simplified: Starting...\r\n");

    // 简化版本：直接生成一个基于检测频率的正弦波
    // 避免复杂的IFFT计算

    float fundamental_freq = adc3_fundamental_freq;
    if (fundamental_freq < 100.0f) fundamental_freq = 1000.0f; // 默认1kHz

    printf("ADC3_ReconstructSignal_Simplified: Generating sine wave at %.1f Hz\r\n", fundamental_freq);

    // 生成512点正弦波数据
    for (int i = 0; i < 512; i++) {
        float t = (float)i / 102314.0f;  // 时间
        float sine_value = sinf(2.0f * M_PI * fundamental_freq * t);

        // 转换为DAC格式：0-4095
        float dac_value = 2048.0f + sine_value * 1000.0f;  // 1.65V ± 0.8V

        // 限制范围
        if (dac_value < 0) dac_value = 0;
        if (dac_value > 4095) dac_value = 4095;

        adc3_reconstructed[i] = (uint16_t)dac_value;
    }

    printf("ADC3_ReconstructSignal_Simplified: Sine wave generated\r\n");
}

// ADC3反FFT重构信号函数（原版本）
void ADC3_ReconstructSignal(void)
{
    // 频率分辨率和400kHz截止频率
    float freq_resolution = 102314.0f / ADC3_SAMPLE_SIZE;
    int max_freq_bin = (int)(400000.0f / freq_resolution);
    if (max_freq_bin > ADC3_SAMPLE_SIZE/2) max_freq_bin = ADC3_SAMPLE_SIZE/2;

    // 先清零反FFT输入缓冲区
    for (int i = 0; i < ADC3_SAMPLE_SIZE * 2; i++) {
        adc3_ifft_inputbuf[i] = 0.0f;
    }

    // 智能频率滤波：只保留主要频率分量，避免重影
    printf("ADC3_ReconstructSignal: Smart frequency filtering...\r\n");

    // 清零IFFT输入缓冲区
    for (int i = 0; i < ADC3_SAMPLE_SIZE * 2; i++) {
        adc3_ifft_inputbuf[i] = 0.0f;
    }

    // 保留直流分量
    adc3_ifft_inputbuf[0] = adc3_fft_inputbuf[0];
    adc3_ifft_inputbuf[1] = adc3_fft_inputbuf[1];

    // 找到主要频率分量（幅度最大的几个）
    float amplitude_threshold = 10.0f;  // 幅度阈值
    int preserved_bins = 0;

    // 计算100kHz对应的bin数（使用已声明的freq_resolution）
    int max_bin_100khz = (int)(100000.0f / freq_resolution);
    if (max_bin_100khz > ADC3_SAMPLE_SIZE/2) max_bin_100khz = ADC3_SAMPLE_SIZE/2;

    printf("ADC3_ReconstructSignal: Scanning bins 1 to %d (%.1f kHz)\r\n", max_bin_100khz, max_bin_100khz * freq_resolution / 1000.0f);

    // 只保留显著的频率分量
    for (int i = 1; i < max_bin_100khz; i++) {
        if (adc3_fft_outputbuf[i] > amplitude_threshold) {
            // 计算该频率分量的频率
            float freq = i * freq_resolution;

            // 获取该频率的第二次扫频校正系数
            float sweep_correction_ratio = 1.0f;
            if (use_sweep_correction && sweep_phase2_completed) {
                sweep_correction_ratio = GetSweepPhase2Ratio(freq);
            }

            // 保留这个频率分量并应用扫频校正
            adc3_ifft_inputbuf[2*i] = adc3_fft_inputbuf[2*i] * sweep_correction_ratio;
            adc3_ifft_inputbuf[2*i+1] = adc3_fft_inputbuf[2*i+1] * sweep_correction_ratio;

            // 对应的负频率分量（共轭对称）
            int neg_index = ADC3_SAMPLE_SIZE - i;
            if (neg_index > 0 && neg_index < ADC3_SAMPLE_SIZE) {
                adc3_ifft_inputbuf[2*neg_index] = adc3_fft_inputbuf[2*neg_index] * sweep_correction_ratio;
                adc3_ifft_inputbuf[2*neg_index+1] = adc3_fft_inputbuf[2*neg_index+1] * sweep_correction_ratio;
            }

            preserved_bins++;
            printf("Preserved bin %d: %.1f Hz, amplitude %.1f, 校正系数: %.3f\r\n",
                   i, freq, adc3_fft_outputbuf[i], sweep_correction_ratio);
        }
    }

    printf("ADC3_ReconstructSignal: Preserved %d significant frequency bins\r\n", preserved_bins);

    // 优化为512点IFFT以减少计算量
    // 清零512点缓冲区
    for (int i = 0; i < 512 * 2; i++) {
        adc3_ifft_512_buf[i] = 0.0f;
    }

    // 关键：512点FFT到512点IFFT的直接映射
    // 直接复制所有频率分量（512点FFT结果直接用于512点IFFT）
    for (int i = 0; i < ADC3_SAMPLE_SIZE * 2; i++) {
        adc3_ifft_512_buf[i] = adc3_ifft_inputbuf[i];
    }

    // 执行512点IFFT（减少计算量）
    printf("ADC3_ProcessFFT: Starting 512-point IFFT...\r\n");
    printf("ADC3_ProcessFFT: IFFT input ready, first few values: %.2f, %.2f, %.2f\r\n",
           adc3_ifft_512_buf[0], adc3_ifft_512_buf[2], adc3_ifft_512_buf[4]);

    // 执行IFFT（注意：ARM CMSIS的IFFT需要手动缩放）
    arm_cfft_f32(&arm_cfft_sR_f32_len512, adc3_ifft_512_buf, 1, 1);

    // 重要：IFFT后需要除以N来得到正确的幅度
    for (int i = 0; i < ADC3_SAMPLE_SIZE * 2; i++) {
        adc3_ifft_512_buf[i] /= (float)ADC3_SAMPLE_SIZE;
    }

    printf("ADC3_ProcessFFT: IFFT completed with scaling\r\n");

    // 强制周期性：让最后一个点等于第一个点，确保DMA循环时无断点
    printf("ADC3_ProcessFFT: Enforcing periodicity to eliminate discontinuities\r\n");

    float first_real = adc3_ifft_512_buf[0];
    float last_real = adc3_ifft_512_buf[2*511];
    printf("ADC3_ProcessFFT: Before periodicity fix - first=%.3f, last=%.3f, diff=%.3f\r\n",
           first_real, last_real, last_real - first_real);

    // 方法1：简单强制 - 让最后一个点等于第一个点
    adc3_ifft_512_buf[2*511] = adc3_ifft_512_buf[0];  // 实部
    adc3_ifft_512_buf[2*511+1] = adc3_ifft_512_buf[1];  // 虚部（虽然应该是0）

    printf("ADC3_ProcessFFT: Periodicity enforced - last point set to first point\r\n");

    // 检查IFFT结果的合理性
    float first_val = adc3_ifft_512_buf[0];
    if (first_val > 1000.0f || first_val < -1000.0f) {
        printf("ADC3_ProcessFFT: Warning - IFFT result may be invalid: %.2f\r\n", first_val);
    }

    printf("ADC3_ProcessFFT: IFFT output first few values: %.2f, %.2f, %.2f\r\n",
           adc3_ifft_512_buf[0], adc3_ifft_512_buf[2], adc3_ifft_512_buf[4]);



    // 将512点IFFT结果转换为DAC输出格式
    printf("ADC3_ReconstructSignal: Converting IFFT to DAC format...\r\n");
    float max_val = 0.0f, min_val = 0.0f;

    // 找到重构信号的最大值和最小值（使用原始IFFT值）
    for (int i = 0; i < 512; i++) {
        float real_part = adc3_ifft_512_buf[2*i];
        if (real_part > max_val) max_val = real_part;
        if (real_part < min_val) min_val = real_part;
    }

    printf("ADC3_ReconstructSignal: IFFT range: %.2f to %.2f\r\n", min_val, max_val);

    // 优化的DAC转换：保持信号的相对幅度关系
    printf("ADC3_ReconstructSignal: Optimized DAC conversion...\r\n");

    // 计算信号的RMS值，用于更好的缩放
    float rms = 0.0f;
    for (int i = 0; i < 512; i++) {
        float real_part = adc3_ifft_512_buf[2*i];
        rms += real_part * real_part;
    }
    rms = sqrtf(rms / 512.0f);

    // 计算缩放参数，基于RMS而不是峰值
    float target_rms_dac = 300.0f;  // 目标RMS对应的DAC单位
    float scale = (rms > 0.001f) ? (target_rms_dac / rms) : 1.0f;
    float offset = 2048.0f;  // 1.65V偏置

    printf("ADC3_ReconstructSignal: RMS=%.3f, Scale=%.1f\r\n", rms, scale);

    // 计算DC偏移（应该接近0，因为我们已经去除了DC）
    float dc_offset = 0.0f;
    for (int i = 0; i < 512; i++) {
        dc_offset += adc3_ifft_512_buf[2*i];
    }
    dc_offset /= 512.0f;

    printf("ADC3_ReconstructSignal: DC offset=%.3f\r\n", dc_offset);

    for (int i = 0; i < 512; i++) {
        // 去除DC偏移，然后缩放
        float real_part = adc3_ifft_512_buf[2*i] - dc_offset;
        float dac_value = offset + real_part * scale;

        // 限制在DAC范围内
        if (dac_value < 0) dac_value = 0;
        if (dac_value > 4095) dac_value = 4095;

        adc3_reconstructed[i] = (uint16_t)dac_value;
    }

    printf("ADC3_ReconstructSignal: DAC conversion completed\r\n");
    printf("ADC3_ReconstructSignal: Final DAC range: %d to %d\r\n",
           adc3_reconstructed[0], adc3_reconstructed[100]);

    // 检查IFFT重构数据的边界连续性
    uint16_t boundary_diff = abs((int)adc3_reconstructed[511] - (int)adc3_reconstructed[0]);
    printf("ADC3_ReconstructSignal: Boundary continuity check: first=%d, last=%d, diff=%d\r\n",
           adc3_reconstructed[0], adc3_reconstructed[511], boundary_diff);

    if (boundary_diff > 200) {
        printf("ADC3_ReconstructSignal: Warning - Large boundary discontinuity detected!\r\n");
    }

}


// 启动优化的DAC输出
void ADC3_StartOptimizedDACOutput(void)
{
    printf("ADC3_StartOptimizedDACOutput: Starting with optimized parameters\r\n");

    // 使用之前计算的优化参数
    printf("ADC3: Using freq=%.1fHz, points=%d, rate=%.0fHz\r\n",
           adc3_fundamental_freq, adc3_points_per_cycle, adc3_dac_sample_rate);

    // 新方案：提取IFFT信号中一个基频周期的完整波形
    printf("ADC3: Extracting one complete cycle based on fundamental frequency\r\n");

    // 计算基频对应的周期长度（以采样点为单位）
    float adc3_sample_rate = 102314.0f;  // ADC3采样率
    float fundamental_freq = adc3_fundamental_freq;

    if (fundamental_freq < 100.0f || fundamental_freq > 50000.0f) {
        printf("ADC3: Invalid fundamental frequency %.1f Hz, using 1kHz\r\n", fundamental_freq);
        fundamental_freq = 1000.0f;
    }

    // 计算一个基频周期需要多少个采样点
    float points_per_fundamental_cycle = adc3_sample_rate / fundamental_freq;
    int cycle_points = (int)(points_per_fundamental_cycle + 0.5f);  // 四舍五入

    // 限制周期点数在合理范围内
    if (cycle_points < 8) cycle_points = 8;
    if (cycle_points > 512) cycle_points = 512;

    printf("ADC3: Fundamental %.1f Hz -> %.1f points/cycle -> %d points\r\n",
           fundamental_freq, points_per_fundamental_cycle, cycle_points);

    // 从IFFT重构的512点数据中提取一个完整周期
    // 512点代表 512/102314 ≈ 5ms 的信号时间
    float ifft_time_duration = 512.0f / adc3_sample_rate;
    float cycles_in_ifft = fundamental_freq * ifft_time_duration;

    printf("ADC3: IFFT contains %.2f cycles of fundamental frequency\r\n", cycles_in_ifft);

    // 如果IFFT中包含至少一个完整周期，直接提取
    if (cycles_in_ifft >= 1.0f) {
        // 提取第一个完整周期
        for (int i = 0; i < cycle_points; i++) {
            if (i < 512) {
                adc3_cycle_buffer[i] = adc3_reconstructed[i];
            } else {
                // 如果周期点数超过512，重复使用数据
                adc3_cycle_buffer[i] = adc3_reconstructed[i % 512];
            }
        }

        printf("ADC3: Extracted first complete cycle (%d points)\r\n", cycle_points);
    } else {
        // 如果IFFT中不足一个周期，使用整个IFFT数据作为周期
        cycle_points = 512;
        for (int i = 0; i < cycle_points; i++) {
            adc3_cycle_buffer[i] = adc3_reconstructed[i];
        }

        printf("ADC3: Using entire IFFT data as cycle (%d points)\r\n", cycle_points);
    }

    // 强制周期性：确保最后一个点平滑过渡到第一个点
    uint16_t first_point = adc3_cycle_buffer[0];
    uint16_t last_point = adc3_cycle_buffer[cycle_points - 1];
    int boundary_diff = abs((int)last_point - (int)first_point);

    printf("ADC3: Cycle boundary check: first=%d, last=%d, diff=%d\r\n",
           first_point, last_point, boundary_diff);

    // 如果边界差异较大，应用平滑过渡
    if (boundary_diff > 50) {
        printf("ADC3: Applying boundary smoothing for seamless loop\r\n");

        // 平滑最后5%的点，让它们向第一个点过渡
        int smooth_count = cycle_points / 20;  // 5%
        if (smooth_count < 2) smooth_count = 2;
        if (smooth_count > 10) smooth_count = 10;

        for (int i = 0; i < smooth_count; i++) {
            int idx = cycle_points - smooth_count + i;
            float ratio = (float)(i + 1) / (float)(smooth_count + 1);
            float smoothed = (float)last_point * (1.0f - ratio) + (float)first_point * ratio;
            adc3_cycle_buffer[idx] = (uint16_t)smoothed;
        }

        printf("ADC3: Smoothed last %d points for continuity\r\n", smooth_count);
    }

    // 更新实际使用的参数
    adc3_points_per_cycle = cycle_points;
    adc3_dac_sample_rate = fundamental_freq * (float)cycle_points;

    printf("ADC3: Final cycle parameters: %d points, %.0f Hz DAC rate\r\n",
           adc3_points_per_cycle, adc3_dac_sample_rate);

    // 启动DAC循环输出一个完整的基频周期
    printf("ADC3: Starting DAC cyclic output of one fundamental period\r\n");
    printf("ADC3: Output frequency will be: %.1f Hz (fundamental frequency)\r\n",
           adc3_dac_sample_rate / (float)adc3_points_per_cycle);

    // 验证输出频率
    float actual_output_freq = adc3_dac_sample_rate / (float)adc3_points_per_cycle;
    float freq_error = fabs(actual_output_freq - fundamental_freq) / fundamental_freq * 100.0f;

    printf("ADC3: Frequency verification:\r\n");
    printf("  Target: %.1f Hz (from FFT)\r\n", fundamental_freq);
    printf("  Actual: %.1f Hz (DAC output)\r\n", actual_output_freq);
    printf("  Error:  %.2f%%\r\n", freq_error);

    if (freq_error > 5.0f) {
        printf("ADC3: Warning - Output frequency error > 5%%\r\n");
    }

    DAC_StartOptimizedReconstructedOutput(adc3_dac_sample_rate, adc3_cycle_buffer, adc3_points_per_cycle);
    printf("ADC3_StartOptimizedDACOutput: Cyclic fundamental period output started\r\n");
}

// 谐波滤波函数：只保留基波及其整数倍谐波
void ADC3_HarmonicFiltering(int max_freq_bin, float freq_resolution)
{
    // 找到基波频率 - 扩大搜索范围到奈奎斯特频率
    float max_amplitude = 0.0f;
    int fundamental_bin = 0;
    int min_search_bin = (int)(20.0f / freq_resolution);
    if (min_search_bin < 1) min_search_bin = 1;

    // 搜索范围扩大到奈奎斯特频率
    float nyquist_freq = 102314.0f / 2.0f;  // 51.157kHz
    int max_search_bin = (int)(nyquist_freq / freq_resolution);
    if (max_search_bin > ADC3_SAMPLE_SIZE/2) max_search_bin = ADC3_SAMPLE_SIZE/2;

    for (int i = min_search_bin; i < max_search_bin; i++) {
        if (adc3_fft_outputbuf[i] > max_amplitude) {
            max_amplitude = adc3_fft_outputbuf[i];
            fundamental_bin = i;
        }
    }

    // 保留直流分量
    adc3_ifft_inputbuf[0] = adc3_fft_inputbuf[0];
    adc3_ifft_inputbuf[1] = adc3_fft_inputbuf[1];

    // 计算基波频率
    float fundamental_freq = fundamental_bin * freq_resolution;

    // 首先保留并输出基波
    adc3_ifft_inputbuf[2*fundamental_bin] = adc3_fft_inputbuf[2*fundamental_bin];
    adc3_ifft_inputbuf[2*fundamental_bin+1] = adc3_fft_inputbuf[2*fundamental_bin+1];
    int neg_index = ADC3_SAMPLE_SIZE - fundamental_bin;
    adc3_ifft_inputbuf[2*neg_index] = adc3_fft_inputbuf[2*neg_index];
    adc3_ifft_inputbuf[2*neg_index+1] = adc3_fft_inputbuf[2*neg_index+1];

    // 输出谐波滤波结果 - 基波及有效谐波
    printf("\r\n=== ADC3 Harmonic Filtering Results ===\r\n");
    printf("Order\tFrequency(Hz)\tAmplitude(V)\tPhase(deg)\r\n");

    // 输出基波信息
    float fundamental_voltage = max_amplitude * (3.3f / 4096.0f);  // 转换为电压值
    float fundamental_phase = atan2(adc3_fft_inputbuf[2*fundamental_bin+1],
                                   adc3_fft_inputbuf[2*fundamental_bin]) * 180.0f / M_PI;
    printf("1\t%.1f\t\t%.6f\t\t%.1f\r\n", fundamental_freq, fundamental_voltage, fundamental_phase);

    int preserved_harmonics = 0;

    // 只有当基波频率较低时才搜索谐波
    if (fundamental_freq < nyquist_freq / 2.0f) {  // 基波小于25.5kHz时才搜索谐波
        // 保留基波的谐波，并输出结果
        for (int harmonic = 2; harmonic <= 20; harmonic++) {
            float expected_harmonic_freq = fundamental_freq * harmonic;

            // 检查谐波是否超出奈奎斯特频率
            if (expected_harmonic_freq > nyquist_freq) {
                break;  // 停止搜索，避免混叠谐波
            }

            int harmonic_bin = fundamental_bin * harmonic;

            if (harmonic_bin <= max_freq_bin && harmonic_bin < ADC3_SAMPLE_SIZE/2) {
                // 搜索真实峰值
                int search_start = harmonic_bin - 2;
                int search_end = harmonic_bin + 2;
                if (search_start < 1) search_start = 1;
                if (search_end >= ADC3_SAMPLE_SIZE/2) search_end = ADC3_SAMPLE_SIZE/2 - 1;

                float peak_amplitude = 0.0f;
                int peak_bin = harmonic_bin;
                for (int j = search_start; j <= search_end; j++) {
                    if (adc3_fft_outputbuf[j] > peak_amplitude) {
                        peak_amplitude = adc3_fft_outputbuf[j];
                        peak_bin = j;
                    }
                }

                // 验证是否为真正的谐波（频率误差在±5%以内）
                float actual_freq = peak_bin * freq_resolution;
                float freq_error = fabs(actual_freq - expected_harmonic_freq) / expected_harmonic_freq;

                // 只保留显著且频率正确的谐波
                if (peak_amplitude > max_amplitude * 0.05f && freq_error < 0.05f) {
                    // 保留频率分量
                    adc3_ifft_inputbuf[2*peak_bin] = adc3_fft_inputbuf[2*peak_bin];
                    adc3_ifft_inputbuf[2*peak_bin+1] = adc3_fft_inputbuf[2*peak_bin+1];
                    int neg_index = ADC3_SAMPLE_SIZE - peak_bin;
                    adc3_ifft_inputbuf[2*neg_index] = adc3_fft_inputbuf[2*neg_index];
                    adc3_ifft_inputbuf[2*neg_index+1] = adc3_fft_inputbuf[2*neg_index+1];

                    // 输出：几次波 频率 幅度(电压) 相位
                    float harmonic_voltage = peak_amplitude * (3.3f / 4096.0f);
                    float harmonic_phase = atan2(adc3_fft_inputbuf[2*peak_bin+1],
                                                adc3_fft_inputbuf[2*peak_bin]) * 180.0f / M_PI;
                    printf("%d\t%.1f\t\t%.6f\t\t%.1f\r\n", harmonic, actual_freq, harmonic_voltage, harmonic_phase);
                    preserved_harmonics++;
                }
            } else {
                break;
            }
        }
    }

    printf("\r\nPreserved Components: 1 fundamental + %d harmonics\r\n", preserved_harmonics);
    printf("=== End of Harmonic Filtering ===\r\n\r\n");
}

// 生成基于检测频率的清洁正弦波
void ADC3_GenerateCleanSineWave(void)
{
    printf("ADC3_GenerateCleanSineWave: Starting...\r\n");

    // 使用检测到的主频率生成正弦波
    float target_freq = adc3_fundamental_freq;

    // 安全检查
    if (target_freq < 10.0f || target_freq > 50000.0f) {
        target_freq = 1000.0f;  // 默认1kHz
        printf("ADC3_GenerateCleanSineWave: Using default 1kHz (detected freq out of range)\r\n");
    }

    printf("ADC3_GenerateCleanSineWave: Generating %.1f Hz sine wave\r\n", target_freq);

    // 生成512点正弦波数据
    // 让这512点代表多个完整的正弦波周期，避免截断

    // 计算在512点中包含多少个完整周期
    float sample_rate = 102314.0f;  // ADC3采样率
    float points_per_cycle = sample_rate / target_freq;  // 一个周期需要多少个点

    // 计算512点中包含的周期数
    float total_cycles = 512.0f / points_per_cycle;
    int complete_cycles = (int)total_cycles;  // 完整周期数
    if (complete_cycles < 1) complete_cycles = 1;

    printf("ADC3_GenerateCleanSineWave: %.1f points/cycle, %d complete cycles in 512 points\r\n",
           points_per_cycle, complete_cycles);

    // 生成包含完整周期的正弦波
    for (int i = 0; i < 512; i++) {
        // 计算相位，确保512点包含完整的周期数
        float phase = 2.0f * M_PI * (float)complete_cycles * (float)i / 512.0f;
        float sine_value = sinf(phase);

        // 转换为DAC格式：1.65V ± 0.8V
        float dac_value = 2048.0f + sine_value * 800.0f;  // 2048 ± 800

        // 限制范围
        if (dac_value < 0) dac_value = 0;
        if (dac_value > 4095) dac_value = 4095;

        adc3_reconstructed[i] = (uint16_t)dac_value;
    }

    printf("ADC3_GenerateCleanSineWave: Generated %d complete cycles\r\n", complete_cycles);
    printf("ADC3_GenerateCleanSineWave: DAC range: %d to %d\r\n",
           adc3_reconstructed[0], adc3_reconstructed[128]);
}

// 专门输出ADC3 FFT分析结果的函数
void ADC3_OutputFFTResults(void)
{
    // 频率分辨率：102.314kHz / 512 ≈ 199.8Hz
    float freq_resolution = 102314.0f / ADC3_SAMPLE_SIZE;

    // 寻找基波频率（最大幅度）
    float max_amplitude = 0.0f;
    int fundamental_bin = 0;

    // 在所有频率范围内寻找最大幅度，跳过直流分量
    for (int i = 1; i < ADC3_SAMPLE_SIZE/2; i++) {
        if (adc3_fft_outputbuf[i] > max_amplitude) {
            max_amplitude = adc3_fft_outputbuf[i];
            fundamental_bin = i;
        }
    }

    // 计算基波频率
    float fundamental_freq = fundamental_bin * freq_resolution;

    // 输出详细的FFT分析结果
    printf("\r\n========== ADC3 FFT Analysis Results ==========\r\n");
    printf("Sampling Rate: 102.314 kHz\r\n");
    printf("FFT Points: %d\r\n", ADC3_SAMPLE_SIZE);
    printf("Frequency Resolution: %.2f Hz\r\n", freq_resolution);
    printf("Analysis Range: 0 - %.1f kHz (Nyquist)\r\n", 102314.0f / 2000.0f);

    // 计算并输出直流分量
    float dc_voltage = adc3_fft_outputbuf[0] * (3.3f / 4096.0f);
    printf("DC Component: %.6f V\r\n", dc_voltage);

    printf("\r\n--- Fundamental and Harmonics ---\r\n");
    printf("Order\tFrequency(Hz)\tAmplitude(V)\tPhase(deg)\tTHD(%%)\r\n");

    // 输出基波信息
    float fundamental_voltage = max_amplitude * (3.3f / 4096.0f);  // 转换为电压值
    float fundamental_phase = atan2(adc3_fft_inputbuf[2*fundamental_bin+1],
                                   adc3_fft_inputbuf[2*fundamental_bin]) * 180.0f / M_PI;
    printf("1\t%.1f\t\t%.6f\t\t%.1f\t\t--\r\n", fundamental_freq, fundamental_voltage, fundamental_phase);

    // 设置幅度阈值，只输出显著的谐波
    float amplitude_threshold = max_amplitude * 0.02f;  // 最大幅度的2%作为阈值
    float voltage_threshold = amplitude_threshold * (3.3f / 4096.0f);

    // 搜索谐波（2次到20次谐波）
    int harmonic_count = 0;
    float total_harmonic_power = 0.0f;  // 用于计算THD
    float nyquist_freq = 102314.0f / 2.0f;  // 奈奎斯特频率

    for (int harmonic = 2; harmonic <= 20 && harmonic_count < 15; harmonic++) {
        float expected_harmonic_freq = fundamental_freq * harmonic;

        // 检查谐波是否超出奈奎斯特频率
        if (expected_harmonic_freq > nyquist_freq) {
            break;
        }

        int harmonic_bin = fundamental_bin * harmonic;
        if (harmonic_bin >= ADC3_SAMPLE_SIZE/2) {
            break;
        }

        // 在谐波附近搜索真实峰值（±3个bin）
        int search_start = harmonic_bin - 3;
        int search_end = harmonic_bin + 3;
        if (search_start < 1) search_start = 1;
        if (search_end >= ADC3_SAMPLE_SIZE/2) search_end = ADC3_SAMPLE_SIZE/2 - 1;

        float peak_amplitude = 0.0f;
        int peak_bin = harmonic_bin;
        for (int j = search_start; j <= search_end; j++) {
            if (adc3_fft_outputbuf[j] > peak_amplitude) {
                peak_amplitude = adc3_fft_outputbuf[j];
                peak_bin = j;
            }
        }

        // 验证是否为真正的谐波（频率误差在±8%以内）
        float actual_freq = peak_bin * freq_resolution;
        float freq_error = fabs(actual_freq - expected_harmonic_freq) / expected_harmonic_freq;

        // 只输出显著且频率正确的谐波
        if (peak_amplitude > amplitude_threshold && freq_error < 0.08f) {
            float harmonic_voltage = peak_amplitude * (3.3f / 4096.0f);
            float harmonic_phase = atan2(adc3_fft_inputbuf[2*peak_bin+1],
                                        adc3_fft_inputbuf[2*peak_bin]) * 180.0f / M_PI;

            // 计算该谐波相对于基波的百分比
            float harmonic_percentage = (harmonic_voltage / fundamental_voltage) * 100.0f;
            total_harmonic_power += harmonic_voltage * harmonic_voltage;

            printf("%d\t%.1f\t\t%.6f\t\t%.1f\t\t%.2f\r\n",
                   harmonic, actual_freq, harmonic_voltage, harmonic_phase, harmonic_percentage);
            harmonic_count++;
        }
    }

    // 计算总谐波失真(THD)
    float fundamental_power = fundamental_voltage * fundamental_voltage;
    float thd_percentage = (fundamental_power > 0) ?
                          sqrt(total_harmonic_power / fundamental_power) * 100.0f : 0.0f;

    printf("\r\n--- Summary ---\r\n");
    printf("Fundamental Frequency: %.1f Hz\r\n", fundamental_freq);
    printf("Fundamental Amplitude: %.6f V\r\n", fundamental_voltage);
    printf("Total Harmonics Found: %d (above %.6f V threshold)\r\n", harmonic_count, voltage_threshold);
    printf("Total Harmonic Distortion (THD): %.2f%%\r\n", thd_percentage);
    printf("Signal-to-Noise Ratio: %.1f dB\r\n", 20.0f * log10f(fundamental_voltage / voltage_threshold));
    printf("===============================================\r\n\r\n");
}

// ADC3三次以内谐波重构标准正弦波函数
void ADC3_ReconstructStandardSineWave(void)
{
    printf("\r\n=== ADC3 Standard Sine Wave Reconstruction (≤3rd Harmonics) ===\r\n");

    // 频率分辨率：102.314kHz / 512 ≈ 199.8Hz
    float freq_resolution = 102314.0f / ADC3_SAMPLE_SIZE;

    // 寻找基波频率（最大幅度）
    float max_amplitude = 0.0f;
    int fundamental_bin = 0;

    // 在所有频率范围内寻找最大幅度，跳过直流分量
    for (int i = 1; i < ADC3_SAMPLE_SIZE/2; i++) {
        if (adc3_fft_outputbuf[i] > max_amplitude) {
            max_amplitude = adc3_fft_outputbuf[i];
            fundamental_bin = i;
        }
    }

    // 计算基波频率
    float fundamental_freq = fundamental_bin * freq_resolution;
    printf("Fundamental frequency detected: %.1f Hz (bin %d)\r\n", fundamental_freq, fundamental_bin);

    // 清零IFFT输入缓冲区
    for (int i = 0; i < ADC3_SAMPLE_SIZE * 2; i++) {
        adc3_ifft_inputbuf[i] = 0.0f;
    }

    // 保留直流分量
    adc3_ifft_inputbuf[0] = adc3_fft_inputbuf[0];
    adc3_ifft_inputbuf[1] = adc3_fft_inputbuf[1];
    printf("DC component preserved: %.6f\r\n", adc3_fft_inputbuf[0]);

    // 获取基波频率的第二次扫频校正系数
    float fundamental_sweep_correction = 1.0f;
    if (use_sweep_correction && sweep_phase2_completed) {
        fundamental_sweep_correction = GetSweepPhase2Ratio(fundamental_freq);
    }

    // 保留基波（1次谐波）并应用扫频校正
    adc3_ifft_inputbuf[2*fundamental_bin] = adc3_fft_inputbuf[2*fundamental_bin] * fundamental_sweep_correction;
    adc3_ifft_inputbuf[2*fundamental_bin+1] = adc3_fft_inputbuf[2*fundamental_bin+1] * fundamental_sweep_correction;
    int neg_index = ADC3_SAMPLE_SIZE - fundamental_bin;
    adc3_ifft_inputbuf[2*neg_index] = adc3_fft_inputbuf[2*neg_index] * fundamental_sweep_correction;
    adc3_ifft_inputbuf[2*neg_index+1] = adc3_fft_inputbuf[2*neg_index+1] * fundamental_sweep_correction;

    float fundamental_voltage = max_amplitude * (3.3f / 4096.0f);
    printf("1st harmonic (fundamental): %.1f Hz, %.6f V, 校正系数: %.3f\r\n",
           fundamental_freq, fundamental_voltage, fundamental_sweep_correction);

    // 设置幅度阈值，只保留显著的谐波
    float amplitude_threshold = max_amplitude * 0.05f;  // 最大幅度的5%作为阈值
    float nyquist_freq = 102314.0f / 2.0f;  // 奈奎斯特频率

    int preserved_harmonics = 1;  // 已保留基波

    // 只搜索2次和3次谐波
    for (int harmonic = 2; harmonic <= 3; harmonic++) {
        float expected_harmonic_freq = fundamental_freq * harmonic;

        // 检查谐波是否超出奈奎斯特频率
        if (expected_harmonic_freq > nyquist_freq) {
            printf("%d次谐波 %.1f Hz 超出奈奎斯特频率，跳过\r\n", harmonic, expected_harmonic_freq);
            break;
        }

        int harmonic_bin = fundamental_bin * harmonic;
        if (harmonic_bin >= ADC3_SAMPLE_SIZE/2) {
            printf("%d次谐波 bin %d 超出范围，跳过\r\n", harmonic, harmonic_bin);
            break;
        }

        // 在谐波附近搜索真实峰值（±2个bin）
        int search_start = harmonic_bin - 2;
        int search_end = harmonic_bin + 2;
        if (search_start < 1) search_start = 1;
        if (search_end >= ADC3_SAMPLE_SIZE/2) search_end = ADC3_SAMPLE_SIZE/2 - 1;

        float peak_amplitude = 0.0f;
        int peak_bin = harmonic_bin;
        for (int j = search_start; j <= search_end; j++) {
            if (adc3_fft_outputbuf[j] > peak_amplitude) {
                peak_amplitude = adc3_fft_outputbuf[j];
                peak_bin = j;
            }
        }

        // 验证是否为真正的谐波（频率误差在±5%以内）
        float actual_freq = peak_bin * freq_resolution;
        float freq_error = fabs(actual_freq - expected_harmonic_freq) / expected_harmonic_freq;

        // 只保留显著且频率正确的谐波
        if (peak_amplitude > amplitude_threshold && freq_error < 0.05f) {
            // 获取该频率的第二次扫频校正系数
            float sweep_correction_ratio = 1.0f;
            if (use_sweep_correction && sweep_phase2_completed) {
                sweep_correction_ratio = GetSweepPhase2Ratio(actual_freq);
            }

            // 保留频率分量并应用扫频校正（正频率和负频率）
            adc3_ifft_inputbuf[2*peak_bin] = adc3_fft_inputbuf[2*peak_bin] * sweep_correction_ratio;
            adc3_ifft_inputbuf[2*peak_bin+1] = adc3_fft_inputbuf[2*peak_bin+1] * sweep_correction_ratio;
            int neg_index = ADC3_SAMPLE_SIZE - peak_bin;
            adc3_ifft_inputbuf[2*neg_index] = adc3_fft_inputbuf[2*neg_index] * sweep_correction_ratio;
            adc3_ifft_inputbuf[2*neg_index+1] = adc3_fft_inputbuf[2*neg_index+1] * sweep_correction_ratio;

            float harmonic_voltage = peak_amplitude * (3.3f / 4096.0f);
            printf("%d次谐波: %.1f Hz, %.6f V (%.1f%% of fundamental), 校正系数: %.3f\r\n",
                   harmonic, actual_freq, harmonic_voltage,
                   (harmonic_voltage / fundamental_voltage) * 100.0f, sweep_correction_ratio);
            preserved_harmonics++;
        } else {
            printf("%d次谐波: %.1f Hz, %.6f V (太小或频率偏差过大，跳过)\r\n",
                   harmonic, actual_freq, peak_amplitude * (3.3f / 4096.0f));
        }
    }

    printf("总共保留 %d 个谐波分量（包括基波）\r\n", preserved_harmonics);

    // 显示扫频校正应用状态
    if (use_sweep_correction && sweep_phase2_completed) {
        printf("已应用第二次扫频校正到所有保留的频率分量\r\n");
    } else {
        printf("未应用扫频校正（扫频数据不可用或未启用）\r\n");
    }

    // 执行512点IFFT重构信号
    printf("执行512点IFFT重构...\r\n");

    // 清零512点缓冲区
    for (int i = 0; i < 512 * 2; i++) {
        adc3_ifft_512_buf[i] = 0.0f;
    }

    // 直接复制频域数据到512点IFFT缓冲区
    for (int i = 0; i < ADC3_SAMPLE_SIZE * 2; i++) {
        adc3_ifft_512_buf[i] = adc3_ifft_inputbuf[i];
    }

    // 执行IFFT（注意：ARM CMSIS的IFFT需要手动缩放）
    arm_cfft_f32(&arm_cfft_sR_f32_len512, adc3_ifft_512_buf, 1, 1);

    // 重要：IFFT后需要除以N来得到正确的幅度
    for (int i = 0; i < ADC3_SAMPLE_SIZE * 2; i++) {
        adc3_ifft_512_buf[i] /= (float)ADC3_SAMPLE_SIZE;
    }

    printf("IFFT完成，开始计算峰峰值...\r\n");

    // 计算重构信号的峰峰值
    float max_val = -1000.0f, min_val = 1000.0f;
    for (int i = 0; i < 512; i++) {
        float real_part = adc3_ifft_512_buf[2*i];
        if (real_part > max_val) max_val = real_part;
        if (real_part < min_val) min_val = real_part;
    }

    float peak_to_peak_voltage = (max_val - min_val) * (3.3f / 4096.0f);
    float rms_voltage = peak_to_peak_voltage / (2.0f * sqrt(2.0f));  // 正弦波RMS = Vpp/(2√2)

    printf("重构信号特性:\r\n");
    printf("  峰峰值: %.6f V\r\n", peak_to_peak_voltage);
    printf("  RMS值: %.6f V\r\n", rms_voltage);
    printf("  最大值: %.6f V\r\n", max_val * (3.3f / 4096.0f));
    printf("  最小值: %.6f V\r\n", min_val * (3.3f / 4096.0f));

    // 转换为DAC输出格式，保持峰峰值
    printf("转换为DAC输出格式...\r\n");

    // 计算缩放参数，使输出信号具有合适的幅度
    float signal_range = max_val - min_val;
    float target_dac_range = 1600.0f;  // 目标DAC范围（约1.3V峰峰值）
    float scale = (signal_range > 0.001f) ? (target_dac_range / signal_range) : 1.0f;
    float offset = 2048.0f;  // 1.65V偏置

    // 计算信号的平均值（应该接近0）
    float signal_mean = (max_val + min_val) / 2.0f;

    for (int i = 0; i < 512; i++) {
        // 去除信号平均值，然后缩放并加偏置
        float real_part = adc3_ifft_512_buf[2*i] - signal_mean;
        float dac_value = offset + real_part * scale;

        // 限制DAC输出范围
        if (dac_value < 0) dac_value = 0;
        if (dac_value > 4095) dac_value = 4095;

        adc3_reconstructed[i] = (uint16_t)dac_value;
    }

    // 计算最终DAC输出的峰峰值
    uint16_t dac_max = 0, dac_min = 4095;
    for (int i = 0; i < 512; i++) {
        if (adc3_reconstructed[i] > dac_max) dac_max = adc3_reconstructed[i];
        if (adc3_reconstructed[i] < dac_min) dac_min = adc3_reconstructed[i];
    }

    float final_peak_to_peak = (dac_max - dac_min) * (3.3f / 4095.0f);

    printf("DAC输出特性:\r\n");
    printf("  DAC范围: %d - %d\r\n", dac_min, dac_max);
    printf("  输出峰峰值: %.6f V\r\n", final_peak_to_peak);
    printf("  输出偏置: %.3f V\r\n", ((dac_max + dac_min) / 2.0f) * (3.3f / 4095.0f));

    // 检查边界连续性
    uint16_t boundary_diff = abs((int)adc3_reconstructed[511] - (int)adc3_reconstructed[0]);
    printf("边界连续性: 首点=%d, 末点=%d, 差值=%d\r\n",
           adc3_reconstructed[0], adc3_reconstructed[511], boundary_diff);

    if (boundary_diff > 100) {
        printf("警告: 检测到较大的边界不连续性!\r\n");
    }

    printf("=== 标准正弦波重构完成 ===\r\n\r\n");
}

// ADC3智能重构函数：根据基频判断输出方式
void ADC3_IntelligentReconstruction(void)
{
    printf("\r\n=== ADC3 Intelligent Signal Reconstruction ===\r\n");

    // 频率分辨率：102.314kHz / 512 ≈ 199.8Hz
    float freq_resolution = 102314.0f / ADC3_SAMPLE_SIZE;

    // 寻找基波频率（最大幅度）
    float max_amplitude = 0.0f;
    int fundamental_bin = 0;

    // 在所有频率范围内寻找最大幅度，跳过直流分量
    for (int i = 1; i < ADC3_SAMPLE_SIZE/2; i++) {
        if (adc3_fft_outputbuf[i] > max_amplitude) {
            max_amplitude = adc3_fft_outputbuf[i];
            fundamental_bin = i;
        }
    }

    // 计算基波频率
    float fundamental_freq = fundamental_bin * freq_resolution;
    printf("Fundamental frequency: %.1f Hz (bin %d), amplitude: %.6f V\r\n",
           fundamental_freq, fundamental_bin, max_amplitude * (3.3f / 4096.0f));

    // 新的判断逻辑：基于基频10kHz阈值
    const float FREQ_THRESHOLD = 10000.0f;  // 10kHz阈值
    bool output_standard_sine = (fundamental_freq >= FREQ_THRESHOLD);

    if (output_standard_sine) {
        printf("\r\n>>> 基频 %.1f Hz >= 10kHz，输出标准正弦波（相位置零，峰峰值2V） <<<\r\n", fundamental_freq);
        ADC3_ReconstructStandardSineWave_2VPP(fundamental_freq);
    } else {
        printf("\r\n>>> 基频 %.1f Hz < 10kHz，执行FFT-IFFT-DAC输出 <<<\r\n", fundamental_freq);

        // 对于低频信号，进行谐波分析以决定重构方式
        float base_threshold = max_amplitude * 0.03f;  // Base threshold: 3% of max amplitude
        float nyquist_freq = 102314.0f / 2.0f;  // Nyquist frequency

        int effective_harmonics = 1;  // Fundamental counts as 1st harmonic
        int max_harmonic_order = 1;   // Start with fundamental as 1st order
        float total_harmonic_power = 0.0f;  // For THD calculation
        float fundamental_power = max_amplitude * max_amplitude;

        printf("Analyzing harmonic complexity (base threshold: %.6f V):\r\n", base_threshold * (3.3f / 4096.0f));

        // Improved harmonic detection with adaptive thresholds
        for (int harmonic = 2; harmonic <= 20; harmonic++) {
            float expected_harmonic_freq = fundamental_freq * harmonic;

            // Check if harmonic exceeds Nyquist frequency
            if (expected_harmonic_freq > nyquist_freq) {
                break;
            }

            int harmonic_bin = fundamental_bin * harmonic;
            if (harmonic_bin >= ADC3_SAMPLE_SIZE/2) {
                break;
            }

            // Adaptive search range based on harmonic order
            int search_range = (harmonic <= 5) ? 3 : 2;  // Wider search for low-order harmonics
            int search_start = harmonic_bin - search_range;
            int search_end = harmonic_bin + search_range;
            if (search_start < 1) search_start = 1;
            if (search_end >= ADC3_SAMPLE_SIZE/2) search_end = ADC3_SAMPLE_SIZE/2 - 1;

            float peak_amplitude = 0.0f;
            int peak_bin = harmonic_bin;
            for (int j = search_start; j <= search_end; j++) {
                if (adc3_fft_outputbuf[j] > peak_amplitude) {
                    peak_amplitude = adc3_fft_outputbuf[j];
                    peak_bin = j;
                }
            }

            // Adaptive threshold based on harmonic order
            float harmonic_threshold = base_threshold;
            if (harmonic <= 3) {
                harmonic_threshold = base_threshold * 0.5f;  // Lower threshold for 2nd and 3rd harmonics
            } else if (harmonic <= 7) {
                harmonic_threshold = base_threshold * 0.8f;  // Slightly lower for mid-order harmonics
            }

            // Verify if it's a real harmonic with adaptive frequency tolerance
            float actual_freq = peak_bin * freq_resolution;
            float freq_error = fabs(actual_freq - expected_harmonic_freq) / expected_harmonic_freq;
            float freq_tolerance = (harmonic <= 5) ? 0.10f : 0.06f;  // More tolerance for low-order harmonics

            // Count effective harmonics with improved criteria
            if (peak_amplitude > harmonic_threshold && freq_error < freq_tolerance) {
                effective_harmonics++;
                max_harmonic_order = harmonic;
                total_harmonic_power += peak_amplitude * peak_amplitude;

                float harmonic_voltage = peak_amplitude * (3.3f / 4096.0f);
                float fundamental_voltage = max_amplitude * (3.3f / 4096.0f);
                printf("  %d harmonic: %.1f Hz, %.6f V (%.1f%% of fundamental, threshold=%.1f%%)\r\n",
                       harmonic, actual_freq, harmonic_voltage,
                       (harmonic_voltage / fundamental_voltage) * 100.0f,
                       (harmonic_threshold / max_amplitude) * 100.0f);
            }
        }

        // Calculate Total Harmonic Distortion (THD)
        float thd_percentage = (fundamental_power > 0.001f) ?
                              sqrt(total_harmonic_power / fundamental_power) * 100.0f : 0.0f;

        printf("Harmonic analysis result: effective_harmonics=%d, max_order=%d, THD=%.2f%%\r\n",
               effective_harmonics, max_harmonic_order, thd_percentage);

        // Apply sweep correction if enabled (before reconstruction)
        if (use_sweep_correction) {
            printf("Applying sweep correction to FFT amplitude spectrum...\r\n");
            ADC3_ApplySweepCorrection();
        }

        // 对于低频信号，根据谐波复杂度选择重构方式
        bool use_complex_reconstruction = false;
        const char* decision_reason = "";

        if (effective_harmonics == 1) {
            // Only fundamental detected
            use_complex_reconstruction = false;
            decision_reason = "Pure fundamental signal (no harmonics detected)";
        } else if (thd_percentage < 5.0f) {
            // Very low THD - essentially a sine wave with noise
            use_complex_reconstruction = false;
            decision_reason = "Very low THD (<5%), treating as pure sine wave";
        } else if (thd_percentage < 15.0f && max_harmonic_order <= 3) {
            // Low THD with only low-order harmonics
            use_complex_reconstruction = false;
            decision_reason = "Low THD with simple harmonic structure";
        } else if (max_harmonic_order <= 2 && thd_percentage < 25.0f) {
            // Only 2nd harmonic with moderate THD
            use_complex_reconstruction = false;
            decision_reason = "Only 2nd harmonic present with moderate THD";
        } else {
            // Complex signal with significant harmonics
            use_complex_reconstruction = true;
            decision_reason = "Complex signal with significant harmonic content";
        }

        // Execute reconstruction based on decision
        if (use_complex_reconstruction) {
            printf(">>> %s, preserving original waveform <<<\r\n", decision_reason);
            ADC3_ReconstructComplexWaveform();
        } else {
            printf(">>> %s, outputting standard sine wave <<<\r\n", decision_reason);
            ADC3_ReconstructPureSineWave(fundamental_freq, max_amplitude);
        }
    }

    printf("=== 智能重构完成 ===\r\n\r\n");
}

// Reconstruct pure sine wave (for simple signals)
void ADC3_ReconstructPureSineWave(float fundamental_freq, float fundamental_amplitude)
{
    printf("Reconstructing standard sine wave: %.1f Hz\r\n", fundamental_freq);

    // Improved frequency and points calculation
    float base_sample_rate = 102314.0f;  // ADC3 base sample rate
    float points_per_cycle_float = base_sample_rate / fundamental_freq;

    printf("Theoretical points per cycle: %.2f\r\n", points_per_cycle_float);

    // Adaptive DAC sample rate and points per cycle calculation
    float dac_sample_rate;
    int points_per_cycle;

    // Correct frequency-based DAC configuration to ensure accurate output frequency
    printf("Calculating DAC parameters for %.1f Hz signal\r\n", fundamental_freq);

    // Step 1: Determine optimal points per cycle based on frequency range
    int target_points;
    if (fundamental_freq >= 20000.0f) {
        target_points = 8;   // High frequency: minimum points for efficiency
        printf("High frequency (≥20kHz): targeting %d points per cycle\r\n", target_points);
    } else if (fundamental_freq >= 5000.0f) {
        target_points = 16;  // Medium-high frequency: balance quality and efficiency
        printf("Medium-high frequency (5-20kHz): targeting %d points per cycle\r\n", target_points);
    } else if (fundamental_freq >= 1000.0f) {
        target_points = 32;  // Medium frequency: good quality
        printf("Medium frequency (1-5kHz): targeting %d points per cycle\r\n", target_points);
    } else if (fundamental_freq >= 100.0f) {
        target_points = 64;  // Low frequency: high quality
        printf("Low frequency (100Hz-1kHz): targeting %d points per cycle\r\n", target_points);
    } else {
        target_points = 128; // Very low frequency: maximum quality
        printf("Very low frequency (<100Hz): targeting %d points per cycle\r\n", target_points);
    }

    // Step 2: Calculate required DAC sample rate for exact frequency match
    float required_sample_rate = fundamental_freq * target_points;
    printf("Required DAC sample rate: %.1f Hz\r\n", required_sample_rate);

    // Step 3: Check if required sample rate is within hardware limits
    const float MAX_DAC_SAMPLE_RATE = 2000000.0f;  // 2MHz limit
    const float MIN_DAC_SAMPLE_RATE = 1000.0f;     // 1kHz minimum

    if (required_sample_rate > MAX_DAC_SAMPLE_RATE) {
        // Sample rate too high: reduce points per cycle
        dac_sample_rate = MAX_DAC_SAMPLE_RATE;
        points_per_cycle = (int)(dac_sample_rate / fundamental_freq + 0.5f);
        printf("Sample rate limited to %.0f Hz, adjusted points per cycle to %d\r\n",
               dac_sample_rate, points_per_cycle);
    } else if (required_sample_rate < MIN_DAC_SAMPLE_RATE) {
        // Sample rate too low: increase points per cycle
        dac_sample_rate = MIN_DAC_SAMPLE_RATE;
        points_per_cycle = (int)(dac_sample_rate / fundamental_freq + 0.5f);
        printf("Sample rate raised to %.0f Hz, adjusted points per cycle to %d\r\n",
               dac_sample_rate, points_per_cycle);
    } else {
        // Sample rate is within limits: use calculated values
        dac_sample_rate = required_sample_rate;
        points_per_cycle = target_points;
        printf("Using calculated sample rate: %.0f Hz, points per cycle: %d\r\n",
               dac_sample_rate, points_per_cycle);
    }

    // Step 4: Final validation and adjustment
    if (points_per_cycle < 4) {
        points_per_cycle = 4;
        dac_sample_rate = fundamental_freq * points_per_cycle;
        printf("Adjusted to minimum 4 points per cycle, sample rate: %.0f Hz\r\n", dac_sample_rate);
    }
    if (points_per_cycle > 512) {
        points_per_cycle = 512;
        dac_sample_rate = fundamental_freq * points_per_cycle;
        printf("Adjusted to maximum 512 points per cycle, sample rate: %.0f Hz\r\n", dac_sample_rate);
    }

    // Step 5: Final verification and output
    float actual_output_freq = dac_sample_rate / (float)points_per_cycle;
    float freq_error = fabs(actual_output_freq - fundamental_freq) / fundamental_freq * 100.0f;

    printf("=== Final DAC Configuration ===\r\n");
    printf("Target frequency: %.1f Hz\r\n", fundamental_freq);
    printf("DAC sample rate: %.0f Hz\r\n", dac_sample_rate);
    printf("Points per cycle: %d\r\n", points_per_cycle);
    printf("Actual output frequency: %.1f Hz\r\n", actual_output_freq);
    printf("Frequency error: %.3f%% (%.1f Hz)\r\n", freq_error, actual_output_freq - fundamental_freq);

    if (freq_error > 0.1f) {
        printf("Warning: Frequency error exceeds 0.1%%\r\n");
    } else {
        printf("Frequency accuracy: GOOD\r\n");
    }
    printf("===============================\r\n");

    // Generate standard sine wave data for one cycle
    printf("Generating standard sine wave data...\r\n");

    // Calculate fundamental amplitude (from FFT results)
    float freq_resolution = 102314.0f / ADC3_SAMPLE_SIZE;
    int fundamental_bin = (int)(fundamental_freq / freq_resolution + 0.5f);

    // Get actual amplitude and phase of fundamental from FFT results
    float real_part = adc3_fft_inputbuf[2*fundamental_bin];
    float imag_part = adc3_fft_inputbuf[2*fundamental_bin+1];
    float fft_amplitude = sqrt(real_part * real_part + imag_part * imag_part);
    float phase = atan2(imag_part, real_part);

    // Convert to voltage amplitude
    float voltage_amplitude = fft_amplitude * (3.3f / 4096.0f);
    printf("FFT fundamental amplitude: %.6f V, phase: %.1f degrees\r\n", voltage_amplitude, phase * 180.0f / M_PI);

    // Calculate appropriate DAC amplitude (target peak-to-peak ~1.2V)
    float target_peak_voltage = 0.6f;  // Target peak voltage
    float target_dac_amplitude = target_peak_voltage * (4095.0f / 3.3f);  // Convert to DAC units

    // If FFT amplitude is too small, use target amplitude; otherwise scale proportionally
    float dac_amplitude;
    if (voltage_amplitude < 0.1f) {
        dac_amplitude = target_dac_amplitude;
        printf("FFT amplitude too small, using target amplitude: %.1f DAC units\r\n", dac_amplitude);
    } else {
        // Keep original amplitude ratio but limit to reasonable range
        dac_amplitude = fft_amplitude;
        if (dac_amplitude > target_dac_amplitude) {
            dac_amplitude = target_dac_amplitude;
        }
        printf("Using FFT amplitude: %.1f DAC units\r\n", dac_amplitude);
    }

    // Get DC offset
    float dc_offset = adc3_fft_inputbuf[0];  // DC component
    if (dc_offset < 1500.0f || dc_offset > 2500.0f) {
        dc_offset = 2048.0f;  // Default 1.65V offset
        printf("Using default DC offset: %.0f (1.65V)\r\n", dc_offset);
    } else {
        printf("Using FFT DC offset: %.0f (%.3fV)\r\n", dc_offset, dc_offset * 3.3f / 4095.0f);
    }

    // Generate one cycle of standard sine wave
    for (int i = 0; i < points_per_cycle; i++) {
        // Calculate phase angle
        float theta = 2.0f * M_PI * (float)i / (float)points_per_cycle + phase;

        // Generate sine wave value
        float sine_value = dac_amplitude * sinf(theta);

        // Convert to DAC format: add DC offset
        float dac_value = dc_offset + sine_value;

        // Limit DAC output range
        if (dac_value < 0) dac_value = 0;
        if (dac_value > 4095) dac_value = 4095;

        adc3_cycle_buffer[i] = (uint16_t)dac_value;
    }

    // Calculate peak-to-peak value of generated sine wave
    uint16_t max_dac = 0, min_dac = 4095;
    for (int i = 0; i < points_per_cycle; i++) {
        if (adc3_cycle_buffer[i] > max_dac) max_dac = adc3_cycle_buffer[i];
        if (adc3_cycle_buffer[i] < min_dac) min_dac = adc3_cycle_buffer[i];
    }

    float output_peak_to_peak = (max_dac - min_dac) * (3.3f / 4095.0f);
    float output_center = ((max_dac + min_dac) / 2.0f) * (3.3f / 4095.0f);

    printf("Standard sine wave characteristics:\r\n");
    printf("  Points per cycle: %d\r\n", points_per_cycle);
    printf("  Output frequency: %.1f Hz\r\n", actual_output_freq);
    printf("  Peak-to-peak: %.6f V\r\n", output_peak_to_peak);
    printf("  Center voltage: %.3f V\r\n", output_center);
    printf("  DAC range: %d - %d\r\n", min_dac, max_dac);

    // Output first few data points for debugging
    printf("Sine wave data first 8 points: ");
    for (int i = 0; i < 8 && i < points_per_cycle; i++) {
        printf("%d ", adc3_cycle_buffer[i]);
    }
    printf("\r\n");

    // Set global variables for DAC output
    adc3_fundamental_freq = actual_output_freq;
    adc3_points_per_cycle = points_per_cycle;
    adc3_dac_sample_rate = dac_sample_rate;  // Use calculated DAC sample rate

    // Verify frequency accuracy (reuse freq_error from earlier calculation)
    if (freq_error > 1.0f) {
        printf("Warning: Output frequency error is large (%.2f%%), may need parameter adjustment\r\n", freq_error);
    }

    // Start cyclic DAC output
    printf("Starting cyclic DAC output...\r\n");
    printf("DAC parameters: sample_rate=%.0f Hz, points_per_cycle=%d, output_freq=%.1f Hz\r\n",
           dac_sample_rate, points_per_cycle, actual_output_freq);

    DAC_StartOptimizedReconstructedOutput(dac_sample_rate, adc3_cycle_buffer, points_per_cycle);

    printf("Standard sine wave reconstruction completed\r\n");
}

// Reconstruct complex waveform (for complex signals, preserve original characteristics)
void ADC3_ReconstructComplexWaveform(void)
{
    printf("Reconstructing complex waveform, preserving original signal characteristics\r\n");

    // Frequency resolution and cutoff frequency
    float freq_resolution = 102314.0f / ADC3_SAMPLE_SIZE;
    int max_freq_bin = (int)(50000.0f / freq_resolution);  // 50kHz cutoff
    if (max_freq_bin > ADC3_SAMPLE_SIZE/2) max_freq_bin = ADC3_SAMPLE_SIZE/2;

    // Clear IFFT input buffer
    for (int i = 0; i < ADC3_SAMPLE_SIZE * 2; i++) {
        adc3_ifft_inputbuf[i] = 0.0f;
    }

    // Preserve DC component
    adc3_ifft_inputbuf[0] = adc3_fft_inputbuf[0];
    adc3_ifft_inputbuf[1] = adc3_fft_inputbuf[1];

    // Find fundamental frequency
    float max_amplitude = 0.0f;
    int fundamental_bin = 0;
    for (int i = 1; i < ADC3_SAMPLE_SIZE/2; i++) {
        if (adc3_fft_outputbuf[i] > max_amplitude) {
            max_amplitude = adc3_fft_outputbuf[i];
            fundamental_bin = i;
        }
    }

    float fundamental_freq = fundamental_bin * freq_resolution;
    float amplitude_threshold = max_amplitude * 0.03f;  // Lower threshold to preserve more harmonics

    printf("Fundamental: %.1f Hz, preservation threshold: %.6f V\r\n",
           fundamental_freq, amplitude_threshold * (3.3f / 4096.0f));

    int preserved_components = 0;

    // Preserve all significant frequency components (no harmonic order limit)
    for (int i = 1; i < max_freq_bin; i++) {
        if (adc3_fft_outputbuf[i] > amplitude_threshold) {
            // 计算该频率分量的频率
            float freq = i * freq_resolution;

            // 获取该频率的第二次扫频校正系数
            float sweep_correction_ratio = 1.0f;
            if (use_sweep_correction && sweep_phase2_completed) {
                sweep_correction_ratio = GetSweepPhase2Ratio(freq);
            }

            // Preserve this frequency component with sweep correction (positive and negative frequencies)
            adc3_ifft_inputbuf[2*i] = adc3_fft_inputbuf[2*i] * sweep_correction_ratio;
            adc3_ifft_inputbuf[2*i+1] = adc3_fft_inputbuf[2*i+1] * sweep_correction_ratio;

            // Corresponding negative frequency component
            int neg_index = ADC3_SAMPLE_SIZE - i;
            if (neg_index > 0 && neg_index < ADC3_SAMPLE_SIZE) {
                adc3_ifft_inputbuf[2*neg_index] = adc3_fft_inputbuf[2*neg_index] * sweep_correction_ratio;
                adc3_ifft_inputbuf[2*neg_index+1] = adc3_fft_inputbuf[2*neg_index+1] * sweep_correction_ratio;
            }

            preserved_components++;

            // Check if it's a harmonic of the fundamental (freq was already calculated above)
            int harmonic_order = (int)(freq / fundamental_freq + 0.5f);
            float harmonic_error = fabs(freq - harmonic_order * fundamental_freq) / fundamental_freq;

            if (harmonic_error < 0.1f && harmonic_order > 1) {
                printf("  Preserved %d harmonic: %.1f Hz, %.6f V, 校正系数: %.3f\r\n",
                       harmonic_order, freq, adc3_fft_outputbuf[i] * (3.3f / 4096.0f), sweep_correction_ratio);
            } else {
                printf("  Preserved frequency component: %.1f Hz, %.6f V, 校正系数: %.3f\r\n",
                       freq, adc3_fft_outputbuf[i] * (3.3f / 4096.0f), sweep_correction_ratio);
            }
        }
    }

    printf("Total preserved frequency components: %d\r\n", preserved_components);

    // 显示扫频校正应用状态
    if (use_sweep_correction && sweep_phase2_completed) {
        printf("已应用第二次扫频校正到所有保留的频率分量\r\n");
    } else {
        printf("未应用扫频校正（扫频数据不可用或未启用）\r\n");
    }

    // Execute 512-point IFFT to reconstruct complete waveform
    for (int i = 0; i < 512 * 2; i++) {
        adc3_ifft_512_buf[i] = 0.0f;
    }

    // Copy data to 512-point buffer
    for (int i = 0; i < ADC3_SAMPLE_SIZE * 2; i++) {
        adc3_ifft_512_buf[i] = adc3_ifft_inputbuf[i];
    }

    // Execute IFFT
    arm_cfft_f32(&arm_cfft_sR_f32_len512, adc3_ifft_512_buf, 1, 1);

    // Scale after IFFT
    for (int i = 0; i < ADC3_SAMPLE_SIZE * 2; i++) {
        adc3_ifft_512_buf[i] /= (float)ADC3_SAMPLE_SIZE;
    }

    // Calculate points per fundamental cycle using same logic as sine wave
    float dac_sample_rate;
    int points_per_cycle;

    // Use same correct frequency calculation as pure sine wave
    printf("Calculating DAC parameters for complex waveform at %.1f Hz\r\n", fundamental_freq);

    // Determine optimal points per cycle (same logic as sine wave)
    int target_points;
    if (fundamental_freq >= 20000.0f) {
        target_points = 8;   // High frequency: minimum points
    } else if (fundamental_freq >= 5000.0f) {
        target_points = 16;  // Medium-high frequency
    } else if (fundamental_freq >= 1000.0f) {
        target_points = 32;  // Medium frequency
    } else if (fundamental_freq >= 100.0f) {
        target_points = 64;  // Low frequency
    } else {
        target_points = 128; // Very low frequency
    }

    // Calculate required DAC sample rate for exact frequency match
    float required_sample_rate = fundamental_freq * target_points;

    // Apply hardware limits
    const float MAX_DAC_SAMPLE_RATE = 2000000.0f;  // 2MHz limit
    const float MIN_DAC_SAMPLE_RATE = 1000.0f;     // 1kHz minimum

    if (required_sample_rate > MAX_DAC_SAMPLE_RATE) {
        dac_sample_rate = MAX_DAC_SAMPLE_RATE;
        points_per_cycle = (int)(dac_sample_rate / fundamental_freq + 0.5f);
    } else if (required_sample_rate < MIN_DAC_SAMPLE_RATE) {
        dac_sample_rate = MIN_DAC_SAMPLE_RATE;
        points_per_cycle = (int)(dac_sample_rate / fundamental_freq + 0.5f);
    } else {
        dac_sample_rate = required_sample_rate;
        points_per_cycle = target_points;
    }

    // Final validation
    if (points_per_cycle < 4) {
        points_per_cycle = 4;
        dac_sample_rate = fundamental_freq * points_per_cycle;
    }
    if (points_per_cycle > 512) {
        points_per_cycle = 512;
        dac_sample_rate = fundamental_freq * points_per_cycle;
    }

    printf("Fundamental cycle analysis:\r\n");
    printf("  Selected DAC sample rate: %.0f Hz\r\n", dac_sample_rate);
    printf("  Points per cycle: %d\r\n", points_per_cycle);

    // Extract one fundamental cycle of complex waveform data from 512-point IFFT result
    printf("Extracting one fundamental cycle of complex waveform data...\r\n");

    // Calculate statistical characteristics of reconstructed signal (for scaling)
    float max_val = -1000.0f, min_val = 1000.0f;
    for (int i = 0; i < 512; i++) {
        float real_part = adc3_ifft_512_buf[2*i];
        if (real_part > max_val) max_val = real_part;
        if (real_part < min_val) min_val = real_part;
    }

    // Correct cycle extraction: find one fundamental period in the 512-point IFFT result
    printf("Extracting one fundamental cycle from 512-point IFFT result...\r\n");

    // Calculate how many samples represent one fundamental cycle in the 512-point IFFT
    float adc_sample_rate = 102314.0f;  // Original ADC sample rate
    float samples_per_fundamental_cycle = adc_sample_rate / fundamental_freq;

    printf("Fundamental cycle analysis:\r\n");
    printf("  ADC sample rate: %.0f Hz\r\n", adc_sample_rate);
    printf("  Fundamental frequency: %.1f Hz\r\n", fundamental_freq);
    printf("  Samples per fundamental cycle in 512-point IFFT: %.2f\r\n", samples_per_fundamental_cycle);

    // Ensure we have at least one complete cycle in the 512 points
    if (samples_per_fundamental_cycle > 512.0f) {
        printf("Warning: Fundamental cycle longer than 512 points, using available data\r\n");
        samples_per_fundamental_cycle = 512.0f;
    }

    // Extract exactly one fundamental cycle from the IFFT result
    int ifft_cycle_length = (int)(samples_per_fundamental_cycle + 0.5f);
    if (ifft_cycle_length < 4) ifft_cycle_length = 4;
    if (ifft_cycle_length > 512) ifft_cycle_length = 512;

    printf("  Using %d samples from IFFT result for one cycle\r\n", ifft_cycle_length);

    // Calculate signal statistics for proper scaling (only from the cycle we'll use)
    float cycle_max = -1000.0f, cycle_min = 1000.0f;
    for (int i = 0; i < ifft_cycle_length; i++) {
        float real_part = adc3_ifft_512_buf[2*i];
        if (real_part > cycle_max) cycle_max = real_part;
        if (real_part < cycle_min) cycle_min = real_part;
    }

    float signal_mean = (cycle_max + cycle_min) / 2.0f;
    float signal_range = cycle_max - cycle_min;

    // Adaptive scaling based on signal characteristics
    float target_dac_range;
    if (signal_range < 500.0f) {
        target_dac_range = 1600.0f;  // Smaller signals get more amplification
    } else if (signal_range < 1500.0f) {
        target_dac_range = 1800.0f;  // Medium signals
    } else {
        target_dac_range = 2000.0f;  // Large signals get less amplification to avoid clipping
    }

    float scale = (signal_range > 0.001f) ? (target_dac_range / signal_range) : 1.0f;
    float offset = 2048.0f;  // 1.65V offset

    printf("  Signal range: %.1f, scale: %.2f, offset: %.0f\r\n", signal_range, scale, offset);

    // Resample the fundamental cycle to the target number of points
    for (int i = 0; i < points_per_cycle; i++) {
        // Map from target cycle points back to IFFT cycle samples
        float source_index = ((float)i * (float)ifft_cycle_length) / (float)points_per_cycle;
        int base_index = (int)source_index;
        float fraction = source_index - base_index;

        // Ensure indices are within bounds
        if (base_index >= ifft_cycle_length - 1) {
            base_index = ifft_cycle_length - 1;
            fraction = 0.0f;
        }

        int next_index = base_index + 1;
        if (next_index >= ifft_cycle_length) {
            next_index = 0;  // Wrap to beginning for periodicity
        }

        // Linear interpolation between adjacent samples
        float real_part1 = adc3_ifft_512_buf[2*base_index];
        float real_part2 = adc3_ifft_512_buf[2*next_index];
        float interpolated_value = real_part1 + fraction * (real_part2 - real_part1);

        // Convert to DAC format with improved scaling
        float dac_value = offset + (interpolated_value - signal_mean) * scale;

        // Soft clipping to avoid harsh cutoffs
        if (dac_value < 0) {
            dac_value = 0;
        } else if (dac_value > 4095) {
            dac_value = 4095;
        }

        adc3_cycle_buffer[i] = (uint16_t)dac_value;
    }

    printf("Cycle extraction completed: %d IFFT samples -> %d DAC points\r\n",
           ifft_cycle_length, points_per_cycle);

    // Verify the extracted cycle
    uint16_t cycle_max_dac = 0, cycle_min_dac = 4095;
    for (int i = 0; i < points_per_cycle; i++) {
        if (adc3_cycle_buffer[i] > cycle_max_dac) cycle_max_dac = adc3_cycle_buffer[i];
        if (adc3_cycle_buffer[i] < cycle_min_dac) cycle_min_dac = adc3_cycle_buffer[i];
    }

    printf("Extracted cycle verification:\r\n");
    printf("  DAC range: %d - %d\r\n", cycle_min_dac, cycle_max_dac);
    printf("  First 4 points: %d, %d, %d, %d\r\n",
           adc3_cycle_buffer[0], adc3_cycle_buffer[1],
           adc3_cycle_buffer[2], adc3_cycle_buffer[3]);
    printf("  Last 4 points: %d, %d, %d, %d\r\n",
           adc3_cycle_buffer[points_per_cycle-4], adc3_cycle_buffer[points_per_cycle-3],
           adc3_cycle_buffer[points_per_cycle-2], adc3_cycle_buffer[points_per_cycle-1]);

    // Calculate actual output frequency
    float actual_output_freq = dac_sample_rate / (float)points_per_cycle;

    // Calculate final DAC output characteristics
    uint16_t dac_max = 0, dac_min = 4095;
    for (int i = 0; i < points_per_cycle; i++) {
        if (adc3_cycle_buffer[i] > dac_max) dac_max = adc3_cycle_buffer[i];
        if (adc3_cycle_buffer[i] < dac_min) dac_min = adc3_cycle_buffer[i];
    }

    float final_peak_to_peak = (dac_max - dac_min) * (3.3f / 4095.0f);
    float output_center = ((dac_max + dac_min) / 2.0f) * (3.3f / 4095.0f);

    printf("Complex waveform characteristics:\r\n");
    printf("  Points per cycle: %d\r\n", points_per_cycle);
    printf("  Output frequency: %.1f Hz (error: %.2f%%)\r\n",
           actual_output_freq, fabs(actual_output_freq - fundamental_freq) / fundamental_freq * 100.0f);
    printf("  Peak-to-peak: %.6f V\r\n", final_peak_to_peak);
    printf("  Center voltage: %.3f V\r\n", output_center);
    printf("  DAC range: %d - %d\r\n", dac_min, dac_max);

    // Check cycle boundary continuity
    uint16_t boundary_diff = abs((int)adc3_cycle_buffer[points_per_cycle-1] - (int)adc3_cycle_buffer[0]);
    printf("Cycle boundary continuity: first_point=%d, last_point=%d, difference=%d\r\n",
           adc3_cycle_buffer[0], adc3_cycle_buffer[points_per_cycle-1], boundary_diff);

    if (boundary_diff > 100) {
        printf("Warning: Large cycle boundary discontinuity detected!\r\n");
    }

    // Set global variables for DAC output
    adc3_fundamental_freq = actual_output_freq;
    adc3_points_per_cycle = points_per_cycle;
    adc3_dac_sample_rate = dac_sample_rate;  // Use calculated DAC sample rate

    // Start cyclic DAC output
    printf("Starting cyclic complex waveform DAC output...\r\n");
    DAC_StartOptimizedReconstructedOutput(dac_sample_rate, adc3_cycle_buffer, points_per_cycle);

    printf("Complex waveform reconstruction completed\r\n");
}

// 扫频校正功能实现

// 加载扫频结果（快速版本，减少输出）
void ADC3_LoadSweepResults(void)
{
    // 清空现有数据
    sweep_correction_count = 0;
    for (int i = 0; i < MAX_SWEEP_POINTS; i++) {
        sweep_correction_data[i].valid = false;
    }

    // 示例：添加一些测试数据（实际使用时应从文件或其他源加载）
    // 这里假设在某些频率点有特定的电压比校正值
    ADC3_AddSweepPoint(100.0f, 1.2f);    // 100Hz处需要1.2倍校正
    ADC3_AddSweepPoint(500.0f, 1.1f);    // 500Hz处需要1.1倍校正
    ADC3_AddSweepPoint(1000.0f, 1.0f);   // 1kHz处不需要校正
    ADC3_AddSweepPoint(2000.0f, 0.9f);   // 2kHz处需要0.9倍校正
    ADC3_AddSweepPoint(5000.0f, 0.8f);   // 5kHz处需要0.8倍校正
    ADC3_AddSweepPoint(10000.0f, 0.7f);  // 10kHz处需要0.7倍校正
    ADC3_AddSweepPoint(20000.0f, 0.6f);  // 20kHz处需要0.6倍校正

    use_sweep_correction = true;
}

// 添加扫频点（快速版本）
void ADC3_AddSweepPoint(float frequency, float voltage_ratio)
{
    if (sweep_correction_count >= MAX_SWEEP_POINTS) {
        return;  // 缓冲区满，直接返回
    }

    sweep_correction_data[sweep_correction_count].frequency = frequency;
    sweep_correction_data[sweep_correction_count].voltage_ratio = voltage_ratio;
    sweep_correction_data[sweep_correction_count].valid = true;
    sweep_correction_count++;
}

// 获取指定频率的电压比（选择最近的频率点，不插值）
float ADC3_GetSweepRatio(float frequency)
{
    if (!use_sweep_correction || sweep_correction_count == 0) {
        return 1.0f;  // 无校正数据时返回1.0
    }

    // 查找最近的频率点
    int nearest_index = -1;
    float min_freq_diff = 1000000.0f;  // 初始化为很大的值

    for (int i = 0; i < sweep_correction_count; i++) {
        if (!sweep_correction_data[i].valid) continue;

        float freq_diff = fabs(sweep_correction_data[i].frequency - frequency);
        if (freq_diff < min_freq_diff) {
            min_freq_diff = freq_diff;
            nearest_index = i;
        }
    }

    // 返回最近频率点的校正比例
    if (nearest_index != -1) {
        return sweep_correction_data[nearest_index].voltage_ratio;
    }

    // 无匹配数据
    return 1.0f;
}

// 应用扫频校正到FFT幅度谱（只对10kHz以下的频率分量）
void ADC3_ApplySweepCorrection(void)
{
    if (!use_sweep_correction || sweep_correction_count == 0) {
        printf("ADC3_ApplySweepCorrection: No correction data available\r\n");
        return;  // 无校正数据，直接返回
    }

    float freq_resolution = 102314.0f / ADC3_SAMPLE_SIZE;
    int max_correction_bin = (int)(10000.0f / freq_resolution);  // 10kHz对应的bin
    if (max_correction_bin > ADC3_SAMPLE_SIZE/2) max_correction_bin = ADC3_SAMPLE_SIZE/2;

    printf("ADC3_ApplySweepCorrection: Applying correction to frequencies below 10kHz (bin 0-%d)\r\n", max_correction_bin);

    int corrected_bins = 0;

    // 只对10kHz以下的频率bin应用校正
    for (int i = 1; i < max_correction_bin; i++) {  // 跳过直流分量，只处理10kHz以下
        // 只对有显著幅度的频率分量应用校正
        if (adc3_fft_outputbuf[i] > 10.0f) {
            float frequency = i * freq_resolution;
            float correction_ratio = ADC3_GetSweepRatio(frequency);

            // 只有当校正比例不为1.0时才进行校正
            if (fabs(correction_ratio - 1.0f) > 0.01f) {
                printf("Correcting bin %d (%.1f Hz): ratio %.3f\r\n", i, frequency, correction_ratio);

                // 直接对复数值应用校正比例
                adc3_fft_inputbuf[2*i] *= correction_ratio;      // 实部
                adc3_fft_inputbuf[2*i+1] *= correction_ratio;    // 虚部

                // 同时更新幅度谱
                adc3_fft_outputbuf[i] *= correction_ratio;

                // 对应的负频率分量也要更新
                int neg_index = ADC3_SAMPLE_SIZE - i;
                if (neg_index > 0 && neg_index < ADC3_SAMPLE_SIZE) {
                    adc3_fft_inputbuf[2*neg_index] *= correction_ratio;
                    adc3_fft_inputbuf[2*neg_index+1] *= correction_ratio;
                }

                corrected_bins++;
            }
        }
    }

    printf("ADC3_ApplySweepCorrection: Corrected %d frequency bins below 10kHz\r\n", corrected_bins);
}

// 扫频校正控制函数
void ADC3_EnableSweepCorrection(bool enable)
{
    use_sweep_correction = enable;
    printf("Sweep correction %s\r\n", enable ? "ENABLED" : "DISABLED");
}

void ADC3_ClearSweepResults(void)
{
    sweep_correction_count = 0;
    for (int i = 0; i < MAX_SWEEP_POINTS; i++) {
        sweep_correction_data[i].valid = false;
    }
    printf("Sweep correction data cleared\r\n");
}

void ADC3_PrintSweepResults(void)
{
    printf("=== Sweep Correction Data ===\r\n");
    printf("Status: %s\r\n", use_sweep_correction ? "ENABLED" : "DISABLED");
    printf("Points: %d/%d\r\n", sweep_correction_count, MAX_SWEEP_POINTS);

    if (sweep_correction_count > 0) {
        printf("Frequency(Hz)\tRatio\r\n");
        for (int i = 0; i < sweep_correction_count; i++) {
            if (sweep_correction_data[i].valid) {
                printf("%.1f\t\t%.3f\r\n", sweep_correction_data[i].frequency, sweep_correction_data[i].voltage_ratio);
            }
        }
    }
    printf("=============================\r\n");
}

// 快速设置扫频数据（用于外部直接设置扫频结果）
void ADC3_SetSweepData(float* frequencies, float* ratios, int count)
{
    // 清空现有数据
    sweep_correction_count = 0;
    for (int i = 0; i < MAX_SWEEP_POINTS; i++) {
        sweep_correction_data[i].valid = false;
    }

    // 添加新数据
    int points_to_add = (count > MAX_SWEEP_POINTS) ? MAX_SWEEP_POINTS : count;
    for (int i = 0; i < points_to_add; i++) {
        sweep_correction_data[i].frequency = frequencies[i];
        sweep_correction_data[i].voltage_ratio = ratios[i];
        sweep_correction_data[i].valid = true;
        sweep_correction_count++;
    }

    use_sweep_correction = (sweep_correction_count > 0);
}

// ADC3重构2V峰峰值标准正弦波函数（相位置零）
void ADC3_ReconstructStandardSineWave_2VPP(float fundamental_freq)
{
    printf("\r\n=== ADC3 Standard Sine Wave Reconstruction (2V P-P, Zero Phase) ===\r\n");
    printf("Target frequency: %.1f Hz\r\n", fundamental_freq);
    printf("Target peak-to-peak: 2.0V\r\n");
    printf("Phase: 0 degrees\r\n");

    // 计算DAC参数以实现精确的频率输出
    // 目标：2V峰峰值 = 1V幅度，相位置零

    // 步骤1：根据频率范围确定每周期的点数
    int target_points;
    if (fundamental_freq >= 20000.0f) {
        target_points = 8;   // 高频：最少点数以提高效率
        printf("High frequency (≥20kHz): targeting %d points per cycle\r\n", target_points);
    } else if (fundamental_freq >= 5000.0f) {
        target_points = 16;  // 中高频：平衡质量和效率
        printf("Medium-high frequency (5-20kHz): targeting %d points per cycle\r\n", target_points);
    } else if (fundamental_freq >= 1000.0f) {
        target_points = 32;  // 中频：良好质量
        printf("Medium frequency (1-5kHz): targeting %d points per cycle\r\n", target_points);
    } else if (fundamental_freq >= 100.0f) {
        target_points = 64;  // 低频：高质量
        printf("Low frequency (100Hz-1kHz): targeting %d points per cycle\r\n", target_points);
    } else {
        target_points = 128; // 极低频：最高质量
        printf("Very low frequency (<100Hz): targeting %d points per cycle\r\n", target_points);
    }

    // 步骤2：计算所需的DAC采样率
    float required_sample_rate = fundamental_freq * target_points;
    printf("Required DAC sample rate: %.1f Hz\r\n", required_sample_rate);

    // 步骤3：检查采样率是否在硬件限制范围内
    const float MAX_DAC_SAMPLE_RATE = 2000000.0f;  // 2MHz限制
    const float MIN_DAC_SAMPLE_RATE = 1000.0f;     // 1kHz最小值

    float dac_sample_rate;
    int points_per_cycle;

    if (required_sample_rate > MAX_DAC_SAMPLE_RATE) {
        // 采样率过高：减少每周期点数
        dac_sample_rate = MAX_DAC_SAMPLE_RATE;
        points_per_cycle = (int)(dac_sample_rate / fundamental_freq + 0.5f);
        printf("Sample rate limited to %.0f Hz, adjusted points per cycle to %d\r\n",
               dac_sample_rate, points_per_cycle);
    } else if (required_sample_rate < MIN_DAC_SAMPLE_RATE) {
        // 采样率过低：增加每周期点数
        dac_sample_rate = MIN_DAC_SAMPLE_RATE;
        points_per_cycle = (int)(dac_sample_rate / fundamental_freq + 0.5f);
        printf("Sample rate raised to %.0f Hz, adjusted points per cycle to %d\r\n",
               dac_sample_rate, points_per_cycle);
    } else {
        // 采样率在限制范围内：使用计算值
        dac_sample_rate = required_sample_rate;
        points_per_cycle = target_points;
        printf("Using calculated sample rate: %.0f Hz, points per cycle: %d\r\n",
               dac_sample_rate, points_per_cycle);
    }

    // 步骤4：最终验证和调整
    if (points_per_cycle < 4) {
        points_per_cycle = 4;
        dac_sample_rate = fundamental_freq * points_per_cycle;
        printf("Adjusted to minimum 4 points per cycle, sample rate: %.0f Hz\r\n", dac_sample_rate);
    }
    if (points_per_cycle > 512) {
        points_per_cycle = 512;
        dac_sample_rate = fundamental_freq * points_per_cycle;
        printf("Adjusted to maximum 512 points per cycle, sample rate: %.0f Hz\r\n", dac_sample_rate);
    }

    // 步骤5：计算实际输出频率和误差
    float actual_output_freq = dac_sample_rate / (float)points_per_cycle;
    float freq_error = fabs(actual_output_freq - fundamental_freq) / fundamental_freq * 100.0f;

    printf("=== Final DAC Configuration ===\r\n");
    printf("Target frequency: %.1f Hz\r\n", fundamental_freq);
    printf("DAC sample rate: %.0f Hz\r\n", dac_sample_rate);
    printf("Points per cycle: %d\r\n", points_per_cycle);
    printf("Actual output frequency: %.1f Hz\r\n", actual_output_freq);
    printf("Frequency error: %.3f%% (%.1f Hz)\r\n", freq_error, actual_output_freq - fundamental_freq);
    printf("===============================\r\n");

    // 步骤6：生成2V峰峰值、相位置零的正弦波数据
    printf("Generating 2V peak-to-peak sine wave data (zero phase)...\r\n");

    // 计算DAC参数：
    // 2V峰峰值 = 1V幅度
    // DAC中心电压：1.65V (对应DAC值2048)
    // DAC幅度：1V对应的DAC值 = 1.0V * (4095/3.3V) ≈ 1241
    float dc_offset = 2048.0f;  // 1.65V中心电压
    float dac_amplitude = 1.0f * (4095.0f / 3.3f);  // 1V幅度对应的DAC值

    printf("DAC parameters: DC offset=%.0f (1.65V), amplitude=%.0f (1.0V)\r\n", dc_offset, dac_amplitude);

    // 生成一个周期的正弦波数据（相位置零）
    for (int i = 0; i < points_per_cycle; i++) {
        // 计算相位角（相位置零，从0开始）
        float theta = 2.0f * M_PI * (float)i / (float)points_per_cycle;

        // 生成正弦波值（相位置零）
        float sine_value = dac_amplitude * sinf(theta);

        // 转换为DAC格式：加上直流偏置
        float dac_value = dc_offset + sine_value;

        // 限制DAC输出范围
        if (dac_value < 0) dac_value = 0;
        if (dac_value > 4095) dac_value = 4095;

        adc3_cycle_buffer[i] = (uint16_t)dac_value;
    }

    // 计算生成的正弦波的峰峰值
    uint16_t max_dac = 0, min_dac = 4095;
    for (int i = 0; i < points_per_cycle; i++) {
        if (adc3_cycle_buffer[i] > max_dac) max_dac = adc3_cycle_buffer[i];
        if (adc3_cycle_buffer[i] < min_dac) min_dac = adc3_cycle_buffer[i];
    }

    float output_peak_to_peak = (max_dac - min_dac) * (3.3f / 4095.0f);
    float output_center = ((max_dac + min_dac) / 2.0f) * (3.3f / 4095.0f);

    printf("Generated sine wave characteristics:\r\n");
    printf("  Points per cycle: %d\r\n", points_per_cycle);
    printf("  Output frequency: %.1f Hz\r\n", actual_output_freq);
    printf("  Peak-to-peak: %.6f V (target: 2.0V)\r\n", output_peak_to_peak);
    printf("  Center voltage: %.3f V (target: 1.65V)\r\n", output_center);
    printf("  DAC range: %d - %d\r\n", min_dac, max_dac);
    printf("  Phase: 0 degrees\r\n");

    // 输出前几个数据点用于调试
    printf("Sine wave data first 8 points: ");
    for (int i = 0; i < 8 && i < points_per_cycle; i++) {
        printf("%d ", adc3_cycle_buffer[i]);
    }
    printf("\r\n");

    // 设置全局变量用于DAC输出
    adc3_fundamental_freq = actual_output_freq;
    adc3_points_per_cycle = points_per_cycle;
    adc3_dac_sample_rate = dac_sample_rate;

    // 验证频率精度
    if (freq_error > 1.0f) {
        printf("Warning: Output frequency error is large (%.2f%%), may need parameter adjustment\r\n", freq_error);
    } else {
        printf("Frequency accuracy: GOOD (error: %.3f%%)\r\n", freq_error);
    }

    // 启动循环DAC输出
    printf("Starting cyclic DAC output...\r\n");
    printf("DAC parameters: sample_rate=%.0f Hz, points_per_cycle=%d, output_freq=%.1f Hz\r\n",
           dac_sample_rate, points_per_cycle, actual_output_freq);

    DAC_StartOptimizedReconstructedOutput(dac_sample_rate, adc3_cycle_buffer, points_per_cycle);

    printf("2V peak-to-peak standard sine wave reconstruction completed\r\n");
    printf("=== End of 2V P-P Sine Wave Reconstruction ===\r\n\r\n");
}

// 第二次扫频数据管理函数实现

// 保存第二次扫频数据（只保存关键频率点以节省内存）
void SaveSweepPhase2Data(float frequency, float voltage_ratio)
{
    if (sweep_phase2_count >= MAX_PHASE2_POINTS) {
        return;  // 缓冲区满，直接返回
    }

    // 只保存每50个点中的一个，或者关键频率点
    static int save_counter = 0;
    save_counter++;

    // 保存条件：每50个点保存一个，或者是关键频率点
    bool should_save = (save_counter % 50 == 0) ||
                       (fabs(frequency - 1000.0f) < 50.0f) ||   // 1kHz附近
                       (fabs(frequency - 5000.0f) < 100.0f) ||  // 5kHz附近
                       (fabs(frequency - 10000.0f) < 100.0f) || // 10kHz附近
                       (fabs(frequency - 20000.0f) < 200.0f) || // 20kHz附近
                       (fabs(frequency - 50000.0f) < 500.0f) || // 50kHz附近
                       (fabs(frequency - 100000.0f) < 1000.0f); // 100kHz附近

    if (should_save) {
        sweep_phase2_data[sweep_phase2_count].frequency = frequency;
        sweep_phase2_data[sweep_phase2_count].voltage_ratio = voltage_ratio;
        sweep_phase2_data[sweep_phase2_count].valid = true;
        sweep_phase2_count++;
    }
}

// 获取第二次扫频的电压比（选择最近的频率点）
float GetSweepPhase2Ratio(float frequency)
{
    if (sweep_phase2_count == 0) {
        return 1.0f;  // 无数据时返回1.0
    }

    // 查找最近的频率点
    int nearest_index = -1;
    float min_freq_diff = 1000000.0f;  // 初始化为很大的值

    for (int i = 0; i < sweep_phase2_count; i++) {
        if (!sweep_phase2_data[i].valid) continue;

        float freq_diff = fabs(sweep_phase2_data[i].frequency - frequency);
        if (freq_diff < min_freq_diff) {
            min_freq_diff = freq_diff;
            nearest_index = i;
        }
    }

    // 返回最近频率点的电压比
    if (nearest_index != -1) {
        return sweep_phase2_data[nearest_index].voltage_ratio;
    }

    // 无匹配数据
    return 1.0f;
}

// 清空第二次扫频数据
void ClearSweepPhase2Data(void)
{
    sweep_phase2_count = 0;
    sweep_phase2_completed = false;
    for (int i = 0; i < MAX_PHASE2_POINTS; i++) {
        sweep_phase2_data[i].valid = false;
    }
    printf("Phase 2 sweep data cleared\r\n");
}

// 将第二次扫频数据加载到校正数组
void LoadSweepPhase2ToCorrection(void)
{
    // 清空现有校正数据
    sweep_correction_count = 0;
    for (int i = 0; i < MAX_SWEEP_POINTS; i++) {
        sweep_correction_data[i].valid = false;
    }

    // 复制第二次扫频数据到校正数组
    int copied_count = 0;
    for (int i = 0; i < sweep_phase2_count && copied_count < MAX_SWEEP_POINTS; i++) {
        if (sweep_phase2_data[i].valid) {
            sweep_correction_data[copied_count].frequency = sweep_phase2_data[i].frequency;
            sweep_correction_data[copied_count].voltage_ratio = sweep_phase2_data[i].voltage_ratio;
            sweep_correction_data[copied_count].valid = true;
            copied_count++;
        }
    }

    sweep_correction_count = copied_count;
    use_sweep_correction = true;

    printf("LoadSweepPhase2ToCorrection: Loaded %d sweep points for correction\r\n", sweep_correction_count);

    // 显示加载的校正数据摘要
    if (sweep_correction_count > 0) {
        printf("Correction data summary:\r\n");
        for (int i = 0; i < sweep_correction_count && i < 10; i++) {
            printf("  %.0f Hz: %.3f\r\n", sweep_correction_data[i].frequency, sweep_correction_data[i].voltage_ratio);
        }
        if (sweep_correction_count > 10) {
            printf("  ... and %d more points\r\n", sweep_correction_count - 10);
        }
    }
}
